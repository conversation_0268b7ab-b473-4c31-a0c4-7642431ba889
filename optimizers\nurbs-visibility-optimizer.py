from pxr import Usd, UsdGeom, Gf
import omni.usd
import math

def get_camera_position_and_transform(camera_prim, time=Usd.TimeCode.Default()):
    """
    获取相机在世界坐标系中的位置和变换矩阵
    """
    try:
        # 获取相机的变换矩阵
        xformable = UsdGeom.Xformable(camera_prim)
        if not xformable:
            print("ERROR: Camera is not xformable")
            return None, None
        
        # 获取世界变换矩阵
        world_transform = xformable.ComputeLocalToWorldTransform(time)
        
        # 提取位置（矩阵的最后一列的前三个元素）
        position = Gf.Vec3f(world_transform[3][0], world_transform[3][1], world_transform[3][2])
        
        print(f"Camera position (world coordinates): {position}")
        print(f"Camera transform matrix:")
        for i in range(4):
            print(f"  [{world_transform[i][0]:8.3f}, {world_transform[i][1]:8.3f}, {world_transform[i][2]:8.3f}, {world_transform[i][3]:8.3f}]")
        
        return position, world_transform
        
    except Exception as e:
        print("ERROR: Failed to get camera position and transform:", e)
        return None, None

def get_camera_position(camera_prim, time=Usd.TimeCode.Default()):
    """
    获取相机在世界坐标系中的位置
    """
    position, _ = get_camera_position_and_transform(camera_prim, time)
    return position

def calculate_distance(point1, point2):
    """
    计算两点之间的欧几里得距离
    """
    return math.sqrt((point1[0] - point2[0])**2 + 
                    (point1[1] - point2[1])**2 + 
                    (point1[2] - point2[2])**2)

def is_camera_inside_bbox(camera_pos, minB, maxB):
    """
    检测相机是否在边界框内部
    """
    return (minB[0] <= camera_pos[0] <= maxB[0] and
            minB[1] <= camera_pos[1] <= maxB[1] and
            minB[2] <= camera_pos[2] <= maxB[2])

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    # 使用正确的Transform方法 - 使用矩阵乘法
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    # 这相当于将 NUREC 坐标系转换为 USD 坐标系
    
    # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
    # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def optimize_nurbs_visibility_by_camera_position(stage, camera_path="/World/Camera", fix_coordinate_system=True, debug_info=False):
    """
    基于相机位置优化nurbs prim的父父节点显示/隐藏
    只显示相机在bounding box内的nurbs prim的父父节点，其他节点都隐藏
    
    Args:
        stage: USD stage
        camera_path: 相机prim路径
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
    """
    print(f">> Starting nurbs visibility optimization based on camera position")
    if fix_coordinate_system:
        print("  Applying coordinate system correction (90, 180, 0) rotation")
    else:
        print("  No coordinate system correction applied")
    
    # 获取相机位置
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print("ERROR: Camera prim not found:", camera_path)
        return

    camera_position = get_camera_position(camera)
    if not camera_position:
        print("ERROR: Failed to get camera position")
        return

    print(f"Camera position: {camera_position}")

    total = 0
    hit = 0
    visible_count = 0
    hidden_count = 0
    processed_parents = set()

    for prim in stage.Traverse():
        total += 1
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue
        hit += 1
        
        if debug_info:
            print(f"Found crop-bounds prim: {prim.GetPath()}")
        
        try:
            minB = Gf.Vec3f(*attr_min.Get())
            maxB = Gf.Vec3f(*attr_max.Get())
            if debug_info:
                print(f"  Bounds: min={minB}, max={maxB}")
        except Exception as e:
            print("  ERR reading bounds:", e)
            continue

        # 获取父父节点
        parent2 = prim.GetParent().GetParent()
        if not parent2 or not parent2.IsValid():
            print("  WARNING: parent.parent invalid for", prim.GetPath())
            continue
        
        # 避免重复处理同一个父父节点
        if parent2.GetPath() in processed_parents:
            continue
        processed_parents.add(parent2.GetPath())
        
        # 应用坐标系修正到边界框
        if fix_coordinate_system:
            minB, maxB = apply_coordinate_system_correction(minB, maxB, debug_info)
        
        # 检查相机是否在边界框内
        is_inside = is_camera_inside_bbox(camera_position, minB, maxB)
        
        # 根据相机是否在边界框内决定可见性
        is_visible = is_inside
        
        # 设置可见性
        vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
        vis_attr.Set(UsdGeom.Tokens.inherited if is_visible else UsdGeom.Tokens.invisible)
        
        if is_visible:
            visible_count += 1
        else:
            hidden_count += 1
            
        if debug_info:
            print(f"  Parent parent: {parent2.GetPath()}")
            print(f"    Bounds: {minB} to {maxB}")
            print(f"    Camera inside bbox: {is_inside}")
            print(f"    Visibility: {'inherited' if is_visible else 'invisible'}")

    print(f"Total prims scanned: {total}")
    print(f"Prims with crop-bounds: {hit}")
    print(f"Unique parent parents processed: {len(processed_parents)}")
    print(f"Visible (camera inside bbox): {visible_count}")
    print(f"Hidden (camera outside bbox): {hidden_count}")

def visualize_camera_pose(camera_prim, stage=None, create_axis_cubes=False, axis_length=6.0, cube_size=0.6, time=Usd.TimeCode.Default()):
    """
    可视化相机位姿，显示位置、旋转和坐标系信息，并可选择创建坐标轴cube
    
    Args:
        camera_prim: 相机prim对象
        stage: USD stage对象（创建cube时需要）
        create_axis_cubes: 是否创建坐标轴cube（默认False）
        axis_length: 轴的长度（默认6.0）
        cube_size: cube的大小（默认0.6）
        time: 时间码（默认Usd.TimeCode.Default()）
    """
    try:
        position, transform = get_camera_position_and_transform(camera_prim, time)
        if not position or not transform:
            print("ERROR: Failed to get camera pose")
            return None
        
        print(f"\n=== 相机位姿可视化 ===")
        print(f"相机路径: {camera_prim.GetPath()}")
        print(f"世界坐标位置: {position}")
        
        # 提取旋转矩阵（3x3部分）
        rotation_matrix = Gf.Matrix3d(
            transform[0][0], transform[0][1], transform[0][2],
            transform[1][0], transform[1][1], transform[1][2],
            transform[2][0], transform[2][1], transform[2][2]
        )
        
        # 计算欧拉角（以度为单位）
        try:
            rotation = Gf.Rotation(rotation_matrix)
            euler_angles = rotation.GetEulerAngles()
            print(f"旋转角度 (度): X={euler_angles[0]:.2f}, Y={euler_angles[1]:.2f}, Z={euler_angles[2]:.2f}")
        except:
            print("无法计算欧拉角")
        
        # 显示坐标系轴方向
        x_axis = Gf.Vec3f(transform[0][0], transform[0][1], transform[0][2])
        y_axis = Gf.Vec3f(transform[1][0], transform[1][1], transform[1][2])
        z_axis = Gf.Vec3f(transform[2][0], transform[2][1], transform[2][2])
        
        print(f"X轴方向: {x_axis}")
        print(f"Y轴方向: {y_axis}")
        print(f"Z轴方向: {z_axis}")
        
        # 检查是否为右手坐标系
        cross_product = Gf.Cross(x_axis, y_axis)
        is_right_handed = Gf.Dot(cross_product, z_axis) > 0
        print(f"坐标系类型: {'右手' if is_right_handed else '左手'}")
        
        # 如果需要创建坐标轴cube
        axis_cubes = None
        if create_axis_cubes and stage:
            print(f"\n--- 创建坐标轴cube可视化 ---")
            axis_cubes = create_simple_camera_axis_cubes(
                camera_prim, 
                stage, 
                axis_length=axis_length, 
                cube_size=cube_size, 
                time=time
            )
            if axis_cubes:
                print(f"✅ 成功创建坐标轴cube可视化")
                print(f"📍 在Omniverse中查看以下路径:")
                for axis, cube in axis_cubes.items():
                    print(f"   {axis}轴: {cube.GetPath()}")
            else:
                print("❌ 创建坐标轴cube失败")
        elif create_axis_cubes and not stage:
            print("⚠️  需要提供stage参数来创建坐标轴cube")
        
        print(f"=== 相机位姿可视化完成 ===\n")
        return axis_cubes
        
    except Exception as e:
        print(f"ERROR: Failed to visualize camera pose: {e}")
        return None

def create_simple_camera_axis_cubes(camera_prim, stage, axis_length=5.0, cube_size=0.5, time=Usd.TimeCode.Default()):
    """
    创建简化的相机坐标轴可视化，使用三个不同颜色的cube
    
    Args:
        camera_prim: 相机prim
        stage: USD stage
        axis_length: 轴的长度
        cube_size: cube的大小
        time: 时间码
    """
    try:
        position, transform = get_camera_position_and_transform(camera_prim, time)
        if not position or not transform:
            print("ERROR: Failed to get camera pose for axis visualization")
            return None
        
        print(f"\n=== 创建简化相机坐标轴可视化 ===")
        print(f"相机位置: {position}")
        print(f"相机路径: {camera_prim.GetPath()}")
        
        # 创建世界坐标系下的轴方向向量
        x_axis = Gf.Vec3f(transform[0][0], transform[0][1], transform[0][2])
        y_axis = Gf.Vec3f(transform[1][0], transform[1][1], transform[1][2])
        z_axis = Gf.Vec3f(transform[2][0], transform[2][1], transform[2][2])
        
        print(f"轴方向向量:")
        print(f"  X轴: {x_axis}")
        print(f"  Y轴: {y_axis}")
        print(f"  Z轴: {z_axis}")
        
        # 创建独立的坐标轴容器prim
        axis_container_path = "/World/CameraAxisVisualization"
        print(f"创建坐标轴容器: {axis_container_path}")
        
        # 检查是否已存在，如果存在则删除
        existing_container = stage.GetPrimAtPath(axis_container_path)
        if existing_container:
            print(f"删除已存在的坐标轴容器: {axis_container_path}")
            stage.RemovePrim(axis_container_path)
        
        # 创建容器prim
        container_prim = stage.DefinePrim(axis_container_path)
        container_prim.SetTypeName("Xform")
        
        # 创建三个cube分别表示X、Y、Z轴
        cubes = {}
        
        # X轴cube（红色）
        x_cube_path = f"{axis_container_path}/X_Axis"
        print(f"创建X轴cube路径: {x_cube_path}")
        
        x_cube = UsdGeom.Cube.Define(stage, x_cube_path)
        x_cube.CreateSizeAttr().Set(cube_size)
        
        # 计算X轴cube的位置（相机位置 + X轴方向 * 长度）
        x_pos = position + x_axis * axis_length
        x_cube.AddTranslateOp().Set(x_pos)
        
        # 设置可见性为inherited
        x_cube.GetVisibilityAttr().Set(UsdGeom.Tokens.inherited)
        
        cubes['X'] = x_cube
        print(f"✅ 创建X轴cube: {x_cube_path}")
        print(f"   位置: {x_pos}")
        print(f"   大小: {cube_size}")
        print(f"   可见性: {x_cube.GetVisibilityAttr().Get()}")
        
        # Y轴cube（绿色）
        y_cube_path = f"{axis_container_path}/Y_Axis"
        print(f"创建Y轴cube路径: {y_cube_path}")
        
        y_cube = UsdGeom.Cube.Define(stage, y_cube_path)
        y_cube.CreateSizeAttr().Set(cube_size)
        
        # 计算Y轴cube的位置
        y_pos = position + y_axis * axis_length
        y_cube.AddTranslateOp().Set(y_pos)
        
        # 设置可见性为inherited
        y_cube.GetVisibilityAttr().Set(UsdGeom.Tokens.inherited)
        
        cubes['Y'] = y_cube
        print(f"✅ 创建Y轴cube: {y_cube_path}")
        print(f"   位置: {y_pos}")
        print(f"   大小: {cube_size}")
        print(f"   可见性: {y_cube.GetVisibilityAttr().Get()}")
        
        # Z轴cube（蓝色）
        z_cube_path = f"{axis_container_path}/Z_Axis"
        print(f"创建Z轴cube路径: {z_cube_path}")
        
        z_cube = UsdGeom.Cube.Define(stage, z_cube_path)
        z_cube.CreateSizeAttr().Set(cube_size)
        
        # 计算Z轴cube的位置
        z_pos = position + z_axis * axis_length
        z_cube.AddTranslateOp().Set(z_pos)
        
        # 设置可见性为inherited
        z_cube.GetVisibilityAttr().Set(UsdGeom.Tokens.inherited)
        
        cubes['Z'] = z_cube
        print(f"✅ 创建Z轴cube: {z_cube_path}")
        print(f"   位置: {z_pos}")
        print(f"   大小: {cube_size}")
        print(f"   可见性: {z_cube.GetVisibilityAttr().Get()}")
        
        # 中心点cube（白色，表示相机位置）
        center_cube_path = f"{axis_container_path}/Center"
        print(f"创建中心点cube路径: {center_cube_path}")
        
        center_cube = UsdGeom.Cube.Define(stage, center_cube_path)
        center_cube.CreateSizeAttr().Set(cube_size * 0.3)  # 中心点cube更小
        center_cube.AddTranslateOp().Set(position)
        
        # 设置可见性为inherited
        center_cube.GetVisibilityAttr().Set(UsdGeom.Tokens.inherited)
        
        cubes['Center'] = center_cube
        print(f"✅ 创建中心点cube: {center_cube_path}")
        print(f"   位置: {position}")
        print(f"   大小: {cube_size * 0.3}")
        print(f"   可见性: {center_cube.GetVisibilityAttr().Get()}")
        
        # 验证创建的cube是否在stage中
        print(f"\n=== 验证创建的cube ===")
        for axis, cube in cubes.items():
            cube_path = cube.GetPath()
            cube_in_stage = stage.GetPrimAtPath(cube_path)
            if cube_in_stage:
                print(f"✅ {axis}轴cube在stage中: {cube_path}")
                print(f"   可见性: {cube_in_stage.GetAttribute('visibility').Get()}")
                print(f"   变换: {cube_in_stage.GetAttribute('xformOp:translate').Get()}")
            else:
                print(f"❌ {axis}轴cube不在stage中: {cube_path}")
        
        print(f"\n=== 简化相机坐标轴可视化创建完成 ===")
        print(f"坐标轴容器路径: {axis_container_path}")
        print(f"轴长度: {axis_length}")
        print(f"Cube大小: {cube_size}")
        
        return cubes
        
    except Exception as e:
        print(f"ERROR: Failed to create simple camera axis visualization: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    
    # 获取相机prim
    # camera_prim = stage.GetPrimAtPath("/World/Camera")
    # if camera_prim:
    #     # 可视化相机位姿并创建坐标轴cube
    #     print("=== 相机位姿可视化 ===")
    #     axis_cubes = visualize_camera_pose(
    #         camera_prim, 
    #         stage=stage, 
    #         create_axis_cubes=True, 
    #         axis_length=6.0, 
    #         cube_size=0.6
    #     )
    
    # 运行nurbs可见性优化（基于相机位置）
    print("\n=== Nurbs可见性优化 ===")
    print("Running nurbs visibility optimization based on camera position...")
    optimize_nurbs_visibility_by_camera_position(
        stage, 
        camera_path="/World/Camera", 
        fix_coordinate_system=True, 
        debug_info=True
    ) 