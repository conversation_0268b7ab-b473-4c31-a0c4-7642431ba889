# 自动相机优化器 (Auto Camera Optimizer)

这个工具可以自动运行 `optimize_by_camera_distance` 函数，支持两种触发模式：
1. **相机移动检测模式** - 当相机移动超过指定距离时自动触发优化
2. **定时器模式** - 每隔固定时间自动触发优化

## 功能特性

- ✅ **相机移动检测** - 实时监控相机位置，移动超过阈值时自动优化
- ✅ **定时触发** - 按固定时间间隔自动运行优化
- ✅ **手动触发** - 随时手动触发优化函数
- ✅ **动态设置** - 运行时可以动态更新优化参数
- ✅ **状态监控** - 实时查看优化器运行状态
- ✅ **防重复调用** - 防止过于频繁的优化调用
- ✅ **错误处理** - 完善的异常处理机制

## 文件说明

- `auto_camera_optimizer.py` - 主要的自动优化器类
- `auto_optimizer_example.py` - 详细的使用示例
- `quick_start_auto_optimizer.py` - 快速启动脚本
- `camera_distance_optimizer.py` - 原有的相机距离优化函数

## 快速开始

### 1. 启动相机移动检测模式

```python
from quick_start_auto_optimizer import quick_start_movement_mode

# 启动相机移动检测模式（相机移动2米以上时触发）
optimizer = quick_start_movement_mode()
```

### 2. 启动定时器模式

```python
from quick_start_auto_optimizer import quick_start_timer_mode

# 启动定时器模式（每10秒触发一次）
optimizer = quick_start_timer_mode(10.0)
```

### 3. 使用预设配置

```python
from quick_start_auto_optimizer import quick_start_preset

# 使用预设配置启动
optimizer = quick_start_preset('movement_sensitive')  # 敏感移动检测
optimizer = quick_start_preset('timer_fast')          # 快速定时器
```

### 4. 手动触发优化

```python
from quick_start_auto_optimizer import quick_manual_optimize

# 手动触发一次优化
quick_manual_optimize()
```

### 5. 停止自动优化

```python
from quick_start_auto_optimizer import quick_stop

# 停止自动优化
quick_stop()
```

## 详细使用

### 创建优化器实例

```python
from auto_camera_optimizer import create_auto_optimizer

# 创建优化器实例
optimizer = create_auto_optimizer(
    camera_path="/World/Camera",
    distance_threshold=50.0,        # 距离阈值50米
    auto_mode="movement",           # 移动检测模式
    movement_threshold=2.0,         # 相机移动2米以上时触发
    debug_info=True,                # 显示调试信息
    fix_coordinate_system=True      # 应用坐标系修正
)

# 启动优化器
optimizer.start()
```

### 更新设置

```python
# 动态更新优化器设置
optimizer.update_settings(
    distance_threshold=75.0,        # 更新距离阈值
    movement_threshold=3.0,         # 更新移动检测阈值
    debug_info=False                # 关闭调试信息
)
```

### 获取状态

```python
# 获取优化器当前状态
status = optimizer.get_status()
print(f"运行状态: {status['is_running']}")
print(f"自动模式: {status['auto_mode']}")
print(f"距离阈值: {status['distance_threshold']}米")
```

## 预设配置

| 预设名称 | 模式 | 触发条件 | 调试信息 |
|---------|------|----------|----------|
| `movement_sensitive` | 移动检测 | 1米移动触发 | 开启 |
| `movement_normal` | 移动检测 | 3米移动触发 | 关闭 |
| `timer_fast` | 定时器 | 每5秒触发 | 开启 |
| `timer_normal` | 定时器 | 每15秒触发 | 关闭 |

## 参数说明

### AutoCameraOptimizer 参数

- `camera_path` (str): 相机prim路径，默认 "/World/Camera"
- `distance_threshold` (float): 距离阈值（米），默认 50.0
- `fix_coordinate_system` (bool): 是否应用坐标系修正，默认 True
- `debug_info` (bool): 是否输出调试信息，默认 False
- `auto_mode` (str): 自动模式，可选 "movement" 或 "timer"
- `timer_interval` (float): 定时器间隔（秒），默认 5.0
- `movement_threshold` (float): 相机移动检测阈值（米），默认 1.0
- `enabled` (bool): 是否启用自动优化，默认 True

### 全局函数

#### start_auto_optimization()
启动自动相机优化

```python
optimizer = start_auto_optimization(
    camera_path="/World/Camera",
    distance_threshold=50.0,
    auto_mode="movement",
    movement_threshold=2.0,
    debug_info=True
)
```

#### stop_auto_optimization()
停止自动相机优化

```python
success = stop_auto_optimization()
```

#### manual_optimize()
手动触发优化

```python
success = manual_optimize()
```

#### get_optimizer_status()
获取优化器状态

```python
status = get_optimizer_status()
```

## 使用场景

### 场景1: 实时相机跟随优化
```python
# 启动敏感的移动检测模式
optimizer = quick_start_preset('movement_sensitive')
# 相机移动1米就会自动触发优化
```

### 场景2: 定期场景更新
```python
# 启动定时器模式，每30秒更新一次
optimizer = quick_start_timer_mode(30.0)
```

### 场景3: 手动控制优化
```python
# 手动触发优化
quick_manual_optimize()
```

## 注意事项

1. **性能考虑**: 移动检测模式每0.5秒检查一次相机位置，如果不需要这么频繁，可以调整 `movement_threshold` 参数
2. **防重复调用**: 系统内置了防重复调用机制，两次优化调用间隔至少0.1秒
3. **错误处理**: 如果相机路径不存在或获取位置失败，系统会跳过该次优化
4. **内存管理**: 优化器会自动重新获取stage，确保使用最新的场景数据

## 故障排除

### 问题1: 优化器没有启动
- 检查相机路径是否正确
- 确认相机prim存在且可访问

### 问题2: 优化没有触发
- 检查移动检测阈值是否设置过大
- 确认相机确实在移动
- 查看调试信息了解详细情况

### 问题3: 优化过于频繁
- 增加移动检测阈值
- 使用定时器模式替代移动检测模式
- 关闭调试信息减少输出

## 示例代码

完整的使用示例请参考 `auto_optimizer_example.py` 文件。

```python
# 完整示例
from quick_start_auto_optimizer import *

# 启动移动检测模式
optimizer = quick_start_movement_mode()

# 等待一段时间观察效果
import time
time.sleep(30)

# 手动触发一次优化
quick_manual_optimize()

# 停止自动优化
quick_stop()
``` 