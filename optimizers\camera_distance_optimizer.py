from pxr import Usd, UsdGeom, Gf
import omni.usd
import omni.kit.app
import omni.kit.commands
import omni.timeline
import omni.ext
import asyncio
import time
import threading
from typing import Optional, Callable, Dict, List, Tuple
import math

class NurecBoundsCache:
    """
    全局缓存类，用于缓存nurec prim的包围盒信息
    避免重复调用GetAttribute()来提高性能
    """
    
    def __init__(self):
        self._cache: Dict[str, Dict] = {}  # prim_path -> bounds_info
        self._stage_id = None
        self._last_update_time = 0
        self._cache_valid = False
        self._lock = threading.RLock()
    
    def _get_stage_id(self, stage):
        """获取stage的唯一标识符"""
        try:
            # 使用stage的根层路径作为标识符
            root_layer = stage.GetRootLayer()
            if root_layer:
                return root_layer.identifier
            return str(id(stage))
        except:
            return str(id(stage))
    
    def is_cache_valid(self, stage):
        """检查缓存是否仍然有效"""
        with self._lock:
            current_stage_id = self._get_stage_id(stage)
            return (self._cache_valid and 
                   self._stage_id == current_stage_id and 
                   len(self._cache) > 0)
    
    def clear_cache(self):
        """清除缓存"""
        with self._lock:
            self._cache.clear()
            self._cache_valid = False
            self._stage_id = None
            self._last_update_time = 0
    
    def update_cache(self, stage, force_update=False):
        """
        更新缓存
        
        Args:
            stage: USD stage
            force_update: 是否强制更新缓存
        """
        with self._lock:
            current_stage_id = self._get_stage_id(stage)
            
            # 检查是否需要更新缓存
            if (not force_update and 
                self._cache_valid and 
                self._stage_id == current_stage_id):
                return
            
            print("Updating NurecBoundsCache...")
            start_time = time.time()
            
            # 清除旧缓存
            self._cache.clear()
            
            # 遍历所有prim并缓存包围盒信息
            total_prims = 0
            cached_prims = 0
            
            for prim in stage.Traverse():
                total_prims += 1
                
                # 检查是否有包围盒属性
                attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
                attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
                
                if not attr_min or not attr_max:
                    continue
                
                try:
                    # 获取包围盒数据
                    min_bounds = Gf.Vec3f(*attr_min.Get())
                    max_bounds = Gf.Vec3f(*attr_max.Get())
                    
                    # 获取父父节点路径
                    parent2 = prim.GetParent().GetParent()
                    if not parent2 or not parent2.IsValid():
                        continue
                    
                    parent2_path = str(parent2.GetPath())
                    
                    # 缓存信息
                    self._cache[str(prim.GetPath())] = {
                        'min_bounds': min_bounds,
                        'max_bounds': max_bounds,
                        'parent2_path': parent2_path,
                        'parent2_prim': parent2,
                        'prim_path': str(prim.GetPath())
                    }
                    
                    cached_prims += 1
                    
                except Exception as e:
                    print(f"Error caching bounds for {prim.GetPath()}: {e}")
                    continue
            
            # 更新缓存状态
            self._stage_id = current_stage_id
            self._cache_valid = True
            self._last_update_time = time.time()
            
            update_time = time.time() - start_time
            print(f"NurecBoundsCache updated: {cached_prims}/{total_prims} prims cached in {update_time:.3f}s")
    
    def get_cached_bounds(self, stage):
        """
        获取缓存的包围盒信息
        
        Returns:
            List of cached bounds info
        """
        with self._lock:
            if not self.is_cache_valid(stage):
                self.update_cache(stage)
            
            return list(self._cache.values())
    
    def get_cache_stats(self):
        """获取缓存统计信息"""
        with self._lock:
            return {
                'cache_size': len(self._cache),
                'cache_valid': self._cache_valid,
                'stage_id': self._stage_id,
                'last_update_time': self._last_update_time
            }

# 全局缓存实例
_global_bounds_cache = NurecBoundsCache()

def get_camera_position_and_transform(camera_prim, time=Usd.TimeCode.Default()):
    """
    获取相机在世界坐标系中的位置和变换矩阵
    """
    try:
        # 获取相机的变换矩阵
        xformable = UsdGeom.Xformable(camera_prim)
        if not xformable:
            print("ERROR: Camera is not xformable")
            return None, None
        
        # 获取世界变换矩阵
        world_transform = xformable.ComputeLocalToWorldTransform(time)
        
        # 提取位置（矩阵的最后一列的前三个元素）
        position = Gf.Vec3f(world_transform[3][0], world_transform[3][1], world_transform[3][2])
        
        print(f"Camera position (world coordinates): {position}")
        # print(f"Camera transform matrix:")
        # for i in range(4):
        #     print(f"  [{world_transform[i][0]:8.3f}, {world_transform[i][1]:8.3f}, {world_transform[i][2]:8.3f}, {world_transform[i][3]:8.3f}]")
        
        return position, world_transform
        
    except Exception as e:
        print("ERROR: Failed to get camera position and transform:", e)
        return None, None

def get_camera_forward_direction(camera_prim, time=Usd.TimeCode.Default()):
    """
    获取相机在世界坐标系中的forward方向向量
    """
    try:
        # 获取相机的变换矩阵
        xformable = UsdGeom.Xformable(camera_prim)
        if not xformable:
            print("ERROR: Camera is not xformable")
            return None
        
        # 获取世界变换矩阵
        world_transform = xformable.ComputeLocalToWorldTransform(time)
        
        # 提取forward方向（矩阵的第三列，即Z轴方向）
        # 在USD中，相机的forward方向通常是-Z轴
        forward = Gf.Vec3f(-world_transform[2][0], -world_transform[2][1], -world_transform[2][2])
        
        # 归一化向量
        length = math.sqrt(forward[0]**2 + forward[1]**2 + forward[2]**2)
        if length > 0:
            forward = Gf.Vec3f(forward[0]/length, forward[1]/length, forward[2]/length)
        
        print(f"Camera forward direction: {forward}")
        return forward
        
    except Exception as e:
        print("ERROR: Failed to get camera forward direction:", e)
        return None

def get_camera_position(camera_prim, time=Usd.TimeCode.Default()):
    """
    获取相机在世界坐标系中的位置
    """
    position, _ = get_camera_position_and_transform(camera_prim, time)
    return position

def calculate_distance(point1, point2):
    """
    计算两点之间的欧几里得距离
    """
    return math.sqrt((point1[0] - point2[0])**2 + 
                    (point1[1] - point2[1])**2 + 
                    (point1[2] - point2[2])**2)

def is_camera_inside_bbox(camera_pos, minB, maxB):
    """
    检测相机是否在边界框内部
    """
    return (minB[0] <= camera_pos[0] <= maxB[0] and
            minB[1] <= camera_pos[1] <= maxB[1] and
            minB[2] <= camera_pos[2] <= maxB[2])

def calculate_distance_to_bbox(camera_pos, minB, maxB):
    """
    计算相机到边界框的最短距离（保持向后兼容）
    """
    # 首先检测相机是否在边界框内部
    if is_camera_inside_bbox(camera_pos, minB, maxB):
        return 0.0
    
    # 计算边界框上距离相机最近的点
    closest_x = max(minB[0], min(camera_pos[0], maxB[0]))
    closest_y = max(minB[1], min(camera_pos[1], maxB[1]))
    closest_z = max(minB[2], min(camera_pos[2], maxB[2]))
    
    # 计算相机到这个最近点的距离
    closest_point = Gf.Vec3f(closest_x, 0, closest_z)
    return calculate_distance(camera_pos, closest_point)

def ray_bbox_intersection(ray_origin, ray_direction, bbox_min, bbox_max):
    """
    计算射线与包围盒的交点
    
    Args:
        ray_origin: 射线起点 (Gf.Vec3f)
        ray_direction: 射线方向向量 (Gf.Vec3f，已归一化)
        bbox_min: 包围盒最小点 (Gf.Vec3f)
        bbox_max: 包围盒最大点 (Gf.Vec3f)
    
    Returns:
        (intersection_point, distance) 或 (None, None) 如果没有交点
    """
    try:
        # 处理射线方向分量为0的情况
        if abs(ray_direction[0]) < 1e-6:
            ray_direction = Gf.Vec3f(1e-6, ray_direction[1], ray_direction[2])
        if abs(ray_direction[1]) < 1e-6:
            ray_direction = Gf.Vec3f(ray_direction[0], 1e-6, ray_direction[2])
        if abs(ray_direction[2]) < 1e-6:
            ray_direction = Gf.Vec3f(ray_direction[0], ray_direction[1], 1e-6)
        
        # 重新归一化
        length = math.sqrt(ray_direction[0]**2 + ray_direction[1]**2 + ray_direction[2]**2)
        ray_direction = Gf.Vec3f(ray_direction[0]/length, ray_direction[1]/length, ray_direction[2]/length)
        
        # 计算射线与包围盒各面的交点参数
        t_min = (bbox_min[0] - ray_origin[0]) / ray_direction[0]
        t_max = (bbox_max[0] - ray_origin[0]) / ray_direction[0]
        
        if t_min > t_max:
            t_min, t_max = t_max, t_min
        
        ty_min = (bbox_min[1] - ray_origin[1]) / ray_direction[1]
        ty_max = (bbox_max[1] - ray_origin[1]) / ray_direction[1]
        
        if ty_min > ty_max:
            ty_min, ty_max = ty_max, ty_min
        
        if t_min > ty_max or ty_min > t_max:
            return None, None
        
        if ty_min > t_min:
            t_min = ty_min
        if ty_max < t_max:
            t_max = ty_max
        
        tz_min = (bbox_min[2] - ray_origin[2]) / ray_direction[2]
        tz_max = (bbox_max[2] - ray_origin[2]) / ray_direction[2]
        
        if tz_min > tz_max:
            tz_min, tz_max = tz_max, tz_min
        
        if t_min > tz_max or tz_min > t_max:
            return None, None
        
        if tz_min > t_min:
            t_min = tz_min
        if tz_max < t_max:
            t_max = tz_max
        
        # 检查是否有有效的交点
        if t_min < 0 and t_max < 0:
            return None, None
        
        # 取最近的交点
        t = t_min if t_min >= 0 else t_max
        if t < 0:
            return None, None
        
        # 计算交点位置
        intersection_point = Gf.Vec3f(
            ray_origin[0] + ray_direction[0] * t,
            ray_origin[1] + ray_direction[1] * t,
            ray_origin[2] + ray_direction[2] * t
        )
        
        return intersection_point, t
        
    except ZeroDivisionError:
        # 处理射线方向分量为0的情况
        return None, None

def calculate_ray_distance_to_bbox(camera_pos, camera_forward, minB, maxB):
    """
    基于相机forward方向计算到包围盒的射线距离
    
    Args:
        camera_pos: 相机位置 (Gf.Vec3f)
        camera_forward: 相机forward方向 (Gf.Vec3f，已归一化)
        minB: 包围盒最小点 (Gf.Vec3f)
        maxB: 包围盒最大点 (Gf.Vec3f)
    
    Returns:
        distance: 射线距离，如果射线不命中包围盒则返回None
    """
    # 首先检测相机是否在边界框内部
    if is_camera_inside_bbox(camera_pos, minB, maxB):
        return 0.0
    
    # 计算射线与包围盒的交点
    intersection_point, ray_distance = ray_bbox_intersection(camera_pos, camera_forward, minB, maxB)
    
    if intersection_point is not None and ray_distance is not None:
        return ray_distance
    
    return None

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    # 使用正确的Transform方法 - 使用矩阵乘法
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    # 这相当于将 NUREC 坐标系转换为 USD 坐标系
    
    # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
    # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def optimize_by_camera_distance(stage, camera_path="/World/Camera", distance_threshold=50.0, fix_coordinate_system=True, debug_info=False, max_blocks=2, use_ray_distance=True, use_cache=True):
    """
    基于相机距离优化nurec prim的父父节点显示/隐藏，支持按距离排序和限制显示数量
    
    Args:
        stage: USD stage
        camera_path: 相机prim路径
        distance_threshold: 距离阈值（米），在此范围内显示，范围外隐藏
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
        max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（True）还是传统距离检测（False）
        use_cache: 是否使用全局缓存（默认True）
    """
    print(f">> Starting camera distance optimization (threshold: {distance_threshold}m, max_blocks: {max_blocks}, use_ray_distance: {use_ray_distance}, use_cache: {use_cache})")
    if fix_coordinate_system:
        print("  Applying coordinate system correction (90, 180, 0) rotation")
    else:
        print("  No coordinate system correction applied")
    
    # 获取相机位置和变换矩阵
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print("ERROR: Camera prim not found:", camera_path)
        return

    camera_position, camera_transform = get_camera_position_and_transform(camera)
    if not camera_position or not camera_transform:
        print("ERROR: Failed to get camera position and transform")
        return

    # 如果使用射线距离检测，获取相机forward方向
    camera_forward = None
    if use_ray_distance:
        camera_forward = get_camera_forward_direction(camera)
        if not camera_forward:
            print("ERROR: Failed to get camera forward direction, falling back to traditional distance")
            use_ray_distance = False

    # 使用缓存或直接遍历
    if use_cache:
        # 使用全局缓存
        cached_bounds = _global_bounds_cache.get_cached_bounds(stage)
        total = len(cached_bounds)
        hit = total
        processed_parents = set()
        block_distances = []
        
        for bounds_info in cached_bounds:
            minB = bounds_info['min_bounds']
            maxB = bounds_info['max_bounds']
            parent2 = bounds_info['parent2_prim']
            parent2_path = bounds_info['parent2_path']
            
            if debug_info:
                print(f"Found cached crop-bounds prim: {bounds_info['prim_path']}")
                print(f"  Bounds: min={minB}, max={maxB}")
            
            # 避免重复处理同一个父父节点
            if parent2_path in processed_parents:
                continue
            processed_parents.add(parent2_path)
            
            # 应用坐标系修正到边界框
            if fix_coordinate_system:
                minB, maxB = apply_coordinate_system_correction(minB, maxB, debug_info)
            
            # 计算距离：使用射线距离检测或传统距离检测
            if use_ray_distance and camera_forward:
                distance = calculate_ray_distance_to_bbox(camera_position, camera_forward, minB, maxB)
                distance_method = "ray"
            else:
                distance = calculate_distance_to_bbox(camera_position, minB, maxB)
                distance_method = "traditional"
            
            # 存储block信息用于排序
            block_distances.append({
                'parent2': parent2,
                'distance': distance,
                'minB': minB,
                'maxB': maxB,
                'path': parent2.GetPath(),
                'distance_method': distance_method
            })
                
            if debug_info:
                print(f"  Parent parent: {parent2_path}")
                print(f"    Bounds: {minB} to {maxB}")
                is_inside = is_camera_inside_bbox(camera_position, minB, maxB)
                print(f"    Camera inside bbox: {is_inside}")
                print(f"    Distance to bbox: {distance}m ({distance_method})")
    else:
        # 原始方法：直接遍历
        total = 0
        hit = 0
        processed_parents = set()
        block_distances = []

        for prim in stage.Traverse():
            total += 1
            attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
            attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
            if not attr_min or not attr_max:
                continue
            hit += 1
            
            if debug_info:
                print(f"Found crop-bounds prim: {prim.GetPath()}")
            
            try:
                minB = Gf.Vec3f(*attr_min.Get())
                maxB = Gf.Vec3f(*attr_max.Get())
                if debug_info:
                    print(f"  Bounds: min={minB}, max={maxB}")
            except Exception as e:
                print("  ERR reading bounds:", e)
                continue

            # 获取父父节点
            parent2 = prim.GetParent().GetParent()
            if not parent2 or not parent2.IsValid():
                print("  WARNING: parent.parent invalid for", prim.GetPath())
                continue
            
            # 避免重复处理同一个父父节点
            if parent2.GetPath() in processed_parents:
                continue
            processed_parents.add(parent2.GetPath())
            
            # 应用坐标系修正到边界框
            if fix_coordinate_system:
                minB, maxB = apply_coordinate_system_correction(minB, maxB, debug_info)
            
            # 计算距离：使用射线距离检测或传统距离检测
            if use_ray_distance and camera_forward:
                distance = calculate_ray_distance_to_bbox(camera_position, camera_forward, minB, maxB)
                distance_method = "ray"
            else:
                distance = calculate_distance_to_bbox(camera_position, minB, maxB)
                distance_method = "traditional"
            
            # 存储block信息用于排序
            block_distances.append({
                'parent2': parent2,
                'distance': distance,
                'minB': minB,
                'maxB': maxB,
                'path': parent2.GetPath(),
                'distance_method': distance_method
            })
                
            if debug_info:
                print(f"  Parent parent: {parent2.GetPath()}")
                print(f"    Bounds: {minB} to {maxB}")
                is_inside = is_camera_inside_bbox(camera_position, minB, maxB)
                print(f"    Camera inside bbox: {is_inside}")
                print(f"    Distance to bbox: {distance}m ({distance_method})")

    # 按距离排序（距离最近的在前）
    # 对于射线距离，None值（不命中）排在最后
    def sort_key(block):
        if block['distance'] is None:
            return float('inf')  # 不命中的排在最后
        return block['distance']
    
    block_distances.sort(key=sort_key)
    
    if debug_info:
        print(f"\nSorted blocks by distance:")
        for i, block in enumerate(block_distances):
            distance_str = f"{block['distance']:.2f}m" if block['distance'] is not None else "no hit"
            print(f"  {i+1}. {block['path']}: {distance_str} ({block['distance_method']})")
    
    # 设置可见性：只显示距离最近的max_blocks个block
    visible_count = 0
    hidden_count = 0
    
    for i, block in enumerate(block_distances):
        parent2 = block['parent2']
        distance = block['distance']
        distance_method = block['distance_method']
        
        # 判断是否应该显示：
        # 1. 在距离阈值范围内（对于射线距离，None表示不命中，应该隐藏）
        # 2. 是距离最近的max_blocks个之一
        # 3. 如果相机在包围盒内，肯定显示
        is_inside = is_camera_inside_bbox(camera_position, block['minB'], block['maxB'])
        
        should_show = False
        if is_inside:
            should_show = True  # 在包围盒内肯定显示
        elif distance is not None and distance <= distance_threshold and i < max_blocks:
            should_show = True  # 在距离阈值内且是最近的max_blocks个之一
        
        # 设置可见性
        vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
        vis_attr.Set(UsdGeom.Tokens.inherited if should_show else UsdGeom.Tokens.invisible)
        
        if should_show:
            visible_count += 1
            reason = "inside bbox" if is_inside else f"distance {distance:.2f}m ({distance_method})"
            if debug_info:
                print(f"  SHOW: {parent2.GetPath()} ({reason}, rank: {i+1})")
        else:
            hidden_count += 1
            reason = "no ray hit" if distance is None else f"distance {distance:.2f}m > threshold"
            if debug_info:
                print(f"  HIDE: {parent2.GetPath()} ({reason}, rank: {i+1})")

    print(f"Total prims scanned: {total}")
    print(f"Prims with crop-bounds: {hit}")
    print(f"Unique parent parents processed: {len(processed_parents)}")
    print(f"Visible: {visible_count}, Hidden: {hidden_count}")
    print(f"Distance threshold: {distance_threshold}m, Max blocks: {max_blocks}")
    print(f"Distance method: {'ray' if use_ray_distance else 'traditional'}")
    print(f"Cache used: {use_cache}")

# 缓存管理函数
def clear_bounds_cache():
    """清除全局包围盒缓存"""
    global _global_bounds_cache
    _global_bounds_cache.clear_cache()
    print("Global bounds cache cleared")

def get_cache_stats():
    """获取缓存统计信息"""
    global _global_bounds_cache
    return _global_bounds_cache.get_cache_stats()

def force_update_cache(stage=None):
    """强制更新缓存"""
    global _global_bounds_cache
    if stage is None:
        stage = omni.usd.get_context().get_stage()
    _global_bounds_cache.update_cache(stage, force_update=True)
    print("Cache force updated")

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    
    # # 获取相机prim
    # camera_prim = stage.GetPrimAtPath("/World/Camera")
    # if camera_prim:
    #     # 可视化相机位姿并创建坐标轴cube
    #     print("=== 相机位姿可视化 ===")
    #     axis_cubes = visualize_camera_pose(
    #         camera_prim, 
    #         stage=stage, 
    #         create_axis_cubes=True, 
    #         axis_length=6.0, 
    #         cube_size=0.6
    #     )
    
    # 运行相机距离优化（带坐标系修正）
    print("\n=== 相机距离优化 ===")
    print("Running camera distance optimization with coordinate system correction...")
    optimize_by_camera_distance(stage, camera_path="/World/Camera", 
                             distance_threshold=50.0, fix_coordinate_system=True, debug_info=True) 