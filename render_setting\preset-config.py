import omni.kit.viewport.utility as vp_utils
import carb.settings
from pxr import UsdGeom
import omni.usd

# ---------- 获取 Stage 和 Viewport ----------
stage = omni.usd.get_context().get_stage()
vp = vp_utils.get_active_viewport()
settings = carb.settings.get_settings()
camera = UsdGeom.Camera(stage.GetPrimAtPath("/World/Camera"))

# ---------- 定义 Preset ----------
presets = {
    "FPS_OPTIMIZED": {
        "resolution": (960, 540),
        "max_bounce": 2,
        "diffuse_bounce": 1,
        "specular_bounce": 1,
        "spp": 1,
        "clipping": (0.1, 200.0),
        "hide_far_objects": True
    },
    "BALANCED": {
        "resolution": (1280, 720),
        "max_bounce": 3,
        "diffuse_bounce": 2,
        "specular_bounce": 2,
        "spp": 2,
        "clipping": (0.1, 500.0),
        "hide_far_objects": False
    },
    "HIGH_QUALITY": {
        "resolution": (1920, 1080),
        "max_bounce": 6,
        "diffuse_bounce": 6,
        "specular_bounce": 6,
        "spp": 4,
        "clipping": (0.1, 1000.0),
        "hide_far_objects": False
    }
}

# ---------- 应用 Preset 函数 ----------
def apply_preset(preset_name):
    if preset_name not in presets:
        print(f"Preset {preset_name} 不存在")
        return

    preset = presets[preset_name]

    # 分辨率
    vp.set_texture_resolution(preset["resolution"])

    # 光线深度
    settings.set("/rtx/raytracing/maxBounce", preset["max_bounce"])
    settings.set("/rtx/raytracing/maxDiffuseBounce", preset["diffuse_bounce"])
    settings.set("/rtx/raytracing/maxSpecularBounce", preset["specular_bounce"])

    # 每像素采样
    settings.set("/rtx/rendermode/spp", preset["spp"])

    # Clipping
    camera.GetClippingRangeAttr().Set(preset["clipping"])

    # 远景可见性
    far_objects = stage.GetPrimAtPath("/World/FarObjects")
    if far_objects.IsValid():
        far_objects.GetAttribute("visibility").Set(
            "invisible" if preset["hide_far_objects"] else "inherited"
        )

    print(f"Preset {preset_name} 已应用 ✅")

# ---------- 示例：切换到 FPS 优化 ----------
apply_preset("HIGH_QUALITY")
