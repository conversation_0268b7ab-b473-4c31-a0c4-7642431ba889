"""
测试距离范围逻辑的正确性
验证max_distance计算和距离范围分配是否合理
"""

import math

def calculate_lod_distance_ranges(lod_configs, maximum_screen_space_error=16.0, screen_width=1920, h_fov=60.0):
    """
    计算每个LOD对应的距离范围，基于SSE选择策略
    """
    # 计算每个LOD在给定SSE阈值下的临界距离
    # 公式：distance = (geometric_error × screen_width) / (2 × SSE × tan(h_fov/2))
    # 含义：在此距离下，该LOD的SSE刚好等于阈值
    tan_half_fov = math.tan(math.radians(h_fov / 2))

    lod_threshold_distances = {}
    for lod_name, geometric_error in lod_configs.items():
        threshold_distance = (geometric_error * screen_width) / (2 * maximum_screen_space_error * tan_half_fov)
        lod_threshold_distances[lod_name] = threshold_distance

    # 根据SSE选择策略构建距离范围
    # 策略：从低细节到高细节，选择第一个满足SSE阈值的LOD
    distance_ranges = {}

    # Low LOD的临界距离：超过此距离，Low LOD就能满足阈值，应该选择Low LOD
    low_threshold = lod_threshold_distances.get("Low", float('inf'))

    # Medium LOD的临界距离：超过此距离，Medium LOD就能满足阈值
    medium_threshold = lod_threshold_distances.get("Medium", float('inf'))

    # 构建距离范围：
    # - 远距离（>Low阈值）：使用Low LOD（性能优先）
    # - 中距离（Medium阈值到Low阈值）：使用Medium LOD
    # - 近距离（0到Medium阈值）：使用High LOD（质量优先）

    distance_ranges["High"] = (0, medium_threshold)
    distance_ranges["Medium"] = (medium_threshold, low_threshold)
    distance_ranges["Low"] = (low_threshold, float('inf'))

    return distance_ranges, lod_threshold_distances

def calculate_sse(geometric_error, distance_to_camera, screen_width=1920, h_fov=60.0):
    """计算屏幕空间误差(SSE)"""
    if distance_to_camera <= 0:
        return float('inf')
    
    sse = (geometric_error * screen_width) / (2 * distance_to_camera * math.tan(math.radians(h_fov / 2)))
    return sse

def test_distance_range_logic():
    """测试距离范围逻辑"""
    print("=== 测试距离范围逻辑 ===\n")
    
    # 测试配置
    lod_configs = {
        "High": 1.0,    # 高质量：0.5米几何误差
        "Medium": 2.0,  # 中质量：2.0米几何误差  
        "Low": 4.0      # 低质量：8.0米几何误差
    }
    
    maximum_sse = 16.0
    screen_width = 1920
    h_fov = 60.0
    
    print("LOD配置:")
    for lod, error in lod_configs.items():
        print(f"  {lod}: {error}m 几何误差")
    
    print(f"\nSSE阈值: {maximum_sse}px")
    print(f"屏幕宽度: {screen_width}px")
    print(f"水平视野角: {h_fov}°")
    
    # 计算距离范围
    distance_ranges, lod_threshold_distances = calculate_lod_distance_ranges(
        lod_configs, maximum_sse, screen_width, h_fov
    )

    print(f"\n=== 临界距离计算 ===")
    for lod, threshold_dist in lod_threshold_distances.items():
        print(f"{lod}: {threshold_dist:.1f}m (在此距离下SSE={maximum_sse}px)")
    
    print(f"\n=== 距离范围分配 ===")
    for lod, (min_dist, max_dist) in distance_ranges.items():
        max_str = "∞" if max_dist == float('inf') else f"{max_dist:.1f}m"
        print(f"{lod}: [{min_dist:.1f}m, {max_str})")
    
    # 验证逻辑正确性
    print(f"\n=== 逻辑验证 ===")
    test_distances = [50, 100, 150, 200, 300, 500, 1000]
    
    for distance in test_distances:
        print(f"\n距离 {distance}m:")
        
        # 计算每个LOD在此距离下的SSE
        for lod, geometric_error in lod_configs.items():
            sse = calculate_sse(geometric_error, distance, screen_width, h_fov)
            acceptable = "✅" if sse <= maximum_sse else "❌"
            print(f"  {lod}: SSE={sse:.2f}px {acceptable}")
        
        # 根据距离范围选择LOD
        selected_lod = None
        for lod, (min_dist, max_dist) in distance_ranges.items():
            if min_dist <= distance < max_dist:
                selected_lod = lod
                break
        
        print(f"  → 选择的LOD: {selected_lod}")
        
        # 验证选择的LOD是否合理
        if selected_lod:
            selected_sse = calculate_sse(lod_configs[selected_lod], distance, screen_width, h_fov)
            if selected_sse <= maximum_sse:
                print(f"  ✅ 验证通过: {selected_lod} LOD的SSE={selected_sse:.2f}px ≤ {maximum_sse}px")
            else:
                print(f"  ❌ 验证失败: {selected_lod} LOD的SSE={selected_sse:.2f}px > {maximum_sse}px")


def select_lod_by_distance_range(lod_configs, distance_to_camera, maximum_screen_space_error=8.0, screen_width=1920, h_fov=60.0, verbose=False):
    """
    基于距离范围选择LOD

    Args:
        lod_configs: LOD配置字典
        distance_to_camera: 相机到对象的距离
        maximum_screen_space_error: 最大可接受屏幕误差阈值
        screen_width: 视口宽度
        h_fov: 水平视野角
        verbose: 是否输出详细信息

    Returns:
        tuple: (selected_lod, lod_info)
    """
    # 计算距离范围
    distance_ranges, lod_max_distances = calculate_lod_distance_ranges(
        lod_configs, maximum_screen_space_error, screen_width, h_fov
    )

    if verbose:
        print(f"距离范围映射 (SSE阈值={maximum_screen_space_error}px):")
        for lod_name in ["High", "Medium", "Low"]:
            if lod_name in distance_ranges:
                min_dist, max_dist = distance_ranges[lod_name]
                max_dist_str = f"{max_dist:.1f}m" if max_dist != float('inf') else "∞"
                print(f"  {lod_name}: {min_dist:.1f}m - {max_dist_str}")
        print(f"当前距离: {distance_to_camera:.1f}m")

    # 根据距离选择LOD
    selected_lod = "Low"  # 默认
    for lod_name, (min_dist, max_dist) in distance_ranges.items():
        if min_dist <= distance_to_camera < max_dist:
            selected_lod = lod_name
            break

    # 计算各LOD的SSE信息（用于显示）
    lod_sse_info = {}
    for lod_name, geometric_error in lod_configs.items():
        sse = calculate_sse(geometric_error, distance_to_camera, screen_width, h_fov)
        lod_sse_info[lod_name] = {
            'geometric_error': geometric_error,
            'sse': sse,
            'max_distance': lod_max_distances.get(lod_name, 0),
            'distance_range': distance_ranges.get(lod_name, (0, 0))
        }

    if verbose:
        print(f"选择结果: {selected_lod} LOD")
        print(f"各LOD的SSE:")
        for lod_name in ["High", "Medium", "Low"]:
            if lod_name in lod_sse_info:
                info = lod_sse_info[lod_name]
                print(f"  {lod_name}: SSE={info['sse']:.2f}px, 最大距离={info['max_distance']:.1f}m")

    return selected_lod, lod_sse_info


def test_different_sse_thresholds():
    """测试不同SSE阈值的影响"""
    print(f"\n\n=== 不同SSE阈值影响测试 ===\n")
    
    config = {"High": 1.0, "Medium": 4.0, "Low": 8.0}
    thresholds = [4.0, 8.0, 16.0, 32.0]
    test_distance = 200  # 固定距离
    
    print(f"配置: {config}")
    print(f"固定距离: {test_distance}m")
    
    print(f"\n{'SSE阈值':<8} {'High范围':<15} {'Medium范围':<15} {'Low范围':<15} {'选择LOD':<8}")
    print(f"{'-'*70}")
    
    for threshold in thresholds:
        distance_ranges, _ = calculate_lod_distance_ranges(config, threshold)
        selected_lod, _ = select_lod_by_distance_range(config, test_distance, threshold)
        
        # 格式化距离范围
        ranges_str = {}
        for lod_name in ["High", "Medium", "Low"]:
            if lod_name in distance_ranges:
                min_dist, max_dist = distance_ranges[lod_name]
                max_dist_str = f"{max_dist:.0f}" if max_dist != float('inf') else "∞"
                ranges_str[lod_name] = f"{min_dist:.0f}-{max_dist_str}"
            else:
                ranges_str[lod_name] = "N/A"
        
        print(f"{threshold:<8} {ranges_str['High']:<15} {ranges_str['Medium']:<15} {ranges_str['Low']:<15} {selected_lod:<8}")


if __name__ == "__main__":
    test_different_sse_thresholds()
