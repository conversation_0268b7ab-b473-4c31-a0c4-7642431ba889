"""
LOD调度系统包
基于屏幕误差和相机距离的LOD调度系统，类似于Cesium 3D Tiles的实现
"""

from .lod_scheduler import (
    LODScheduler,
    BoundingBox,
    LODTile,
    OctreeNode,
    LODLevel
)

from .lod_config import (
    OctreeConfig,
    CameraConfig,
    CentralizedLODConfig,
    DEFAULT_CONFIG,
)

__version__ = "1.0.0"
__author__ = "LOD System Developer"

__all__ = [
    # 主要类
    "LODScheduler",
    "BoundingBox", 
    "LODTile",
    "OctreeNode",
    "LODLevel",
    
    # 配置类
    "OctreeConfig", 
    "CameraConfig",
    "CentralizedLODConfig",
    
    # 预设配置
    "DEFAULT_CONFIG",
] 