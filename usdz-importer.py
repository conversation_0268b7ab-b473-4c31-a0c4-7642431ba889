from pxr import Usd, UsdGeom, Sdf
import omni.usd
import os
import re

def safe_name(name):
    # 去除扩展名，并把非法字符替换为下划线
    name = os.path.splitext(name)[0]
    return re.sub(r'[^a-zA-Z0-9_]', '_', name)

# USDZ文件所在文件夹
usdz_folder = "E:/wanleqi/FlorenzVillage/data/usdz_output/19"
if os.path.exists(usdz_folder):
    print("✅ 文件夹存在！")
    usdz_files = [f for f in os.listdir(usdz_folder) if f.endswith(".usdz")]
    print(f"找到 {len(usdz_files)} 个 USDZ 文件")
else:
    print("❌ 路径不存在，请检查 usdz_folder 是否写错或未挂载")

# 获取当前Stage
stage = omni.usd.get_context().get_stage()

# 起始位置
start_x = 0.0
spacing = 0.0

# 导入每个 USDZ
for i, filename in enumerate(usdz_files):
    clean_name = safe_name(filename)  # ✅ 确保 Prim 路径合法
    prim_path = f"/Imported/{clean_name}"
    filepath = os.path.join(usdz_folder, filename).replace("\\", "/")

    xform = UsdGeom.Xform.Define(stage, Sdf.Path(prim_path))
    # xform.AddTranslateOp().Set((i * 2.0, 0, 0))
    xform.GetPrim().GetReferences().AddReference(filepath)

# 可选：保存场景
# stage.GetRootLayer().Export("/path/to/output_stage.usd")
