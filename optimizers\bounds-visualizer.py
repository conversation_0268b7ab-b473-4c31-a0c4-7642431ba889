from pxr import Usd, UsdGeom, Gf, Sdf, UsdShade
import omni.usd

def make_wireframe_box(stage, parent, minB, maxB, name="BBoxWire", fix_coordinate_system=True, debug_info=False):
    """
    在 parent 下创建一个可见的 wireframe 类型的 box，表示从 minB 到 maxB 范围。
    使用简单的方法创建12条独立的线段。

    Args:
        fix_coordinate_system: 如果为 True，应用坐标系修正以匹配 NUREC 坐标系
        debug_info: 如果为 True，打印调试信息
    """
    # 创建一个 Xform 作为容器
    container_path = parent.GetPath().AppendChild(name)
    container = UsdGeom.Xform.Define(stage, container_path)

    # 定义变换点的函数
    def transform_point(p, transform_matrix):
        point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
        # 使用正确的Transform方法 - 使用矩阵乘法
        transformed = transform_matrix * point4
        return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

    original_minB = minB
    original_maxB = maxB

    # 如果需要修正坐标系，应用变换
    if fix_coordinate_system:
        if debug_info:
            print(f"  Original bounds: {original_minB} to {original_maxB}")
        
        # 应用 (90, 180, 0) 旋转的逆变换到坐标点
        # 这相当于将 NUREC 坐标系转换为 USD 坐标系
        
        # 创建旋转矩阵：先绕 Z 轴旋转 180°，再绕 X 轴旋转 90°
        # 注意：我们需要应用逆变换，所以是 (90, -180, 0) 的变换
        rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
        rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
        transform_matrix = rot_z_180 * rot_x_90

        # 应用变换到边界点
        minB_transformed = transform_point(minB, transform_matrix)
        maxB_transformed = transform_point(maxB, transform_matrix)

        # 重新计算变换后的最小最大值
        all_transformed_points = [
            transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
            transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
            transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
            transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
            transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
            transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
            transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
            transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
        ]

        # 计算变换后的实际边界
        min_x = min(p[0] for p in all_transformed_points)
        max_x = max(p[0] for p in all_transformed_points)
        min_y = min(p[1] for p in all_transformed_points)
        max_y = max(p[1] for p in all_transformed_points)
        min_z = min(p[2] for p in all_transformed_points)
        max_z = max(p[2] for p in all_transformed_points)

        minB = Gf.Vec3f(min_x, min_y, min_z)
        maxB = Gf.Vec3f(max_x, max_y, max_z)
        
        if debug_info:
            print(f"  Transformed bounds: {minB} to {maxB}")

    # 8 corners of the bounding box (使用修正后的坐标)
    corners = [
        Gf.Vec3f(minB[0], minB[1], minB[2]),  # 0: min corner
        Gf.Vec3f(maxB[0], minB[1], minB[2]),  # 1: +X
        Gf.Vec3f(maxB[0], maxB[1], minB[2]),  # 2: +X+Y
        Gf.Vec3f(minB[0], maxB[1], minB[2]),  # 3: +Y
        Gf.Vec3f(minB[0], minB[1], maxB[2]),  # 4: +Z
        Gf.Vec3f(maxB[0], minB[1], maxB[2]),  # 5: +X+Z
        Gf.Vec3f(maxB[0], maxB[1], maxB[2]),  # 6: +X+Y+Z (max corner)
        Gf.Vec3f(minB[0], maxB[1], maxB[2]),  # 7: +Y+Z
    ]

    # Define the 12 edges of the box
    edges = [
        # Bottom face (Z = minB[2])
        (0, 1), (1, 2), (2, 3), (3, 0),
        # Top face (Z = maxB[2])
        (4, 5), (5, 6), (6, 7), (7, 4),
        # Vertical edges
        (0, 4), (1, 5), (2, 6), (3, 7)
    ]

    # Create each edge as a separate BasisCurves
    for i, (start_idx, end_idx) in enumerate(edges):
        edge_path = container_path.AppendChild(f"Edge_{i}")
        edge_curve = UsdGeom.BasisCurves.Define(stage, edge_path)

        # Set the two points for this edge
        edge_points = [corners[start_idx], corners[end_idx]]
        edge_curve.CreatePointsAttr(edge_points)
        edge_curve.CreateCurveVertexCountsAttr([2])  # One line with 2 points
        edge_curve.CreateTypeAttr(UsdGeom.Tokens.linear)

        # Set display properties
        edge_curve.CreateDisplayColorAttr([Gf.Vec3f(1.0, 0.0, 0.0)])  # Red color
        edge_curve.CreateDisplayOpacityAttr([1.0])
        edge_curve.CreateWidthsAttr([3.0, 3.0])  # Width for both points

    return container

def create_test_nurec_prim(stage, prim_path="/World/TestNurec", minBounds=(-1, -1, -1), maxBounds=(1, 1, 1)):
    """
    创建一个测试用的 NUREC prim，包含 omni:nurec:crop:minBounds 和 maxBounds 属性
    """
    # 创建一个 Xform prim 作为测试对象
    xform = UsdGeom.Xform.Define(stage, prim_path)
    prim = xform.GetPrim()

    # 添加 NUREC crop bounds 属性
    min_attr = prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3)
    max_attr = prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3)

    min_attr.Set(Gf.Vec3f(*minBounds))
    max_attr.Set(Gf.Vec3f(*maxBounds))

    print(f"Created test NUREC prim at {prim_path} with bounds {minBounds} to {maxBounds}")
    return prim

def visualize_nurec_wire(stage, fix_coordinate_system=True, debug_info=False):
    print("Visualizing wireframe bounds for NUREC prims")
    if fix_coordinate_system:
        print("  Applying coordinate system correction (-90, 180, 0) rotation")
    else:
        print("  No coordinate system correction applied")
    
    count = 0
    skipped_count = 0

    for prim in stage.Traverse():

        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue

        # Check visibility - skip if prim or its ancestors are invisible
        imageable = UsdGeom.Imageable(prim)
        if imageable:
            visibility = imageable.ComputeVisibility()
            if visibility == UsdGeom.Tokens.invisible:
                skipped_count += 1
                print(f"[SKIP] {prim.GetPath()} - invisible (visibility={visibility})")
                continue

        # Also check parent's parent visibility (the one that gets culled)
        parent2 = prim.GetParent().GetParent() if prim.GetParent() else None
        if parent2 and parent2.IsValid():
            parent2_imageable = UsdGeom.Imageable(parent2)
            if parent2_imageable:
                parent2_visibility = parent2_imageable.ComputeVisibility()
                if parent2_visibility == UsdGeom.Tokens.invisible:
                    skipped_count += 1
                    print(f"[SKIP] {prim.GetPath()} - parent.parent invisible ({parent2.GetPath()}, visibility={parent2_visibility})")
                    continue

        count += 1
        minB = Gf.Vec3f(*attr_min.Get())
        maxB = Gf.Vec3f(*attr_max.Get())
        print(f"[{count}] {prim.GetPath()} bounds {minB} – {maxB}")

        # 在同一个父级下创建线框，而不是父级的父级
        parent = prim.GetParent()
        if not parent or not parent.IsValid():
            parent = stage.GetDefaultPrim()
        make_wireframe_box(stage, parent, minB, maxB, name=f"WireBBox_{count}", 
                          fix_coordinate_system=fix_coordinate_system, debug_info=debug_info)

    print(f"Wireframe boxes created for {count} prim(s), skipped {skipped_count} invisible prim(s).")


if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    visualize_nurec_wire(stage, fix_coordinate_system=True, debug_info=False)
