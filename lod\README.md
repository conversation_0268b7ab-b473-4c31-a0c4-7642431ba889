# LOD调度系统

这是一个基于屏幕误差和相机距离的LOD（Level of Detail）调度系统，类似于Cesium 3D Tiles的实现。系统使用八叉树来管理区域数据，根据相机位置和视野范围动态调整显示的LOD级别。

## 功能特性

- **基于屏幕误差的LOD选择**：根据对象在屏幕上的投影大小选择合适的LOD级别
- **基于距离的LOD选择**：根据相机到对象的距离选择合适的LOD级别
- **八叉树空间管理**：使用八叉树高效管理3D空间数据
- **视锥体剔除**：只渲染视野范围内的对象
- **递归区域细分**：根据区域面积自动细分，支持多级LOD
- **配置化管理**：支持多种预设配置和自定义配置
- **性能优化**：支持性能监控和优化

## 系统架构

### 核心组件

1. **LODScheduler**：主要的调度器类，负责LOD决策和可见性管理
2. **OctreeNode**：八叉树节点，管理空间分区
3. **BoundingBox**：边界框数据结构，用于空间计算
4. **LODTile**：LOD瓦片数据结构，包含几何和元数据信息
5. **LODSystemConfig**：配置管理器，管理各种系统参数

### LOD级别

系统支持4个LOD级别：
- **HIGH**：高质量，近距离显示
- **MEDIUM**：中等质量，中距离显示
- **LOW**：低质量，远距离显示
- **VERY_LOW**：最低质量，最远距离显示

## 安装和使用

### 基本使用

```python
from pxr import Usd
import omni.usd
from lod.lod_scheduler import LODScheduler

# 获取USD stage
stage = omni.usd.get_context().get_stage()

# 创建LOD调度器
scheduler = LODScheduler(stage, camera_path="/World/Camera")

# 构建八叉树
scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=100.0)

# 更新LOD可见性
scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
```

### 使用配置文件

```python
from lod.lod_config import LODSystemConfig, HIGH_QUALITY_CONFIG

# 使用高质量配置
config = HIGH_QUALITY_CONFIG

# 或者创建自定义配置
custom_config = LODSystemConfig()
custom_config.lod_configs[LODLevel.HIGH].screen_error = 0.5
custom_config.octree_config.max_depth = 8

# 保存配置
config.save_config("my_lod_config.json")

# 加载配置
config.load_config("my_lod_config.json")
```

### 测试系统

```python
# 运行测试
python lod/test_lod_scheduler.py
```

## 配置参数

### LOD配置

每个LOD级别包含以下参数：
- `screen_error`：屏幕误差阈值（像素）
- `distance_threshold`：距离阈值（单位）
- `geometric_error`：几何误差（可选）
- `max_triangles`：最大三角形数量（可选）

### 八叉树配置

- `max_depth`：最大深度（默认8）
- `min_area_threshold`：最小面积阈值（默认100.0）
- `min_volume_threshold`：最小体积阈值（默认1000.0）

### 相机配置

- `fov`：视场角（度，默认60.0）
- `screen_width`：屏幕宽度（默认1920）
- `screen_height`：屏幕高度（默认1080）
- `near_plane`：近平面（默认0.1）
- `far_plane`：远平面（默认1000.0）

### 性能配置

- `max_tiles_per_frame`：每帧最大瓦片数量（默认1000）
- `update_interval`：更新间隔（秒，默认0.1）
- `enable_frustum_culling`：启用视锥体剔除（默认True）
- `enable_occlusion_culling`：启用遮挡剔除（默认False）

## 预设配置

系统提供了三种预设配置：

### 1. 默认配置 (DEFAULT_CONFIG)
平衡质量和性能的配置，适合大多数应用场景。

### 2. 高质量配置 (HIGH_QUALITY_CONFIG)
更严格的LOD阈值，提供更高质量的渲染，但性能消耗更大。

### 3. 性能配置 (PERFORMANCE_CONFIG)
更宽松的LOD阈值，优先考虑性能，适合实时应用。

## 工作原理

### 1. 空间分区
系统使用八叉树将3D空间递归分割成更小的区域，每个区域包含相应的LOD瓦片。

### 2. LOD决策
对于每个可见区域，系统计算：
- 屏幕误差：对象在屏幕上的投影大小
- 距离：相机到对象中心的距离
- 根据这两个参数选择最合适的LOD级别

### 3. 可见性管理
- 只显示视野范围内的区域
- 根据LOD级别显示或隐藏相应的瓦片
- 支持递归细分，在需要时显示更详细的子区域

### 4. 递归细分
当区域满足以下条件时会进行细分：
- 当前深度小于最大深度
- 区域面积大于最小面积阈值
- 屏幕误差大于最高LOD级别的阈值

## 性能优化建议

1. **调整八叉树深度**：根据场景复杂度调整`max_depth`
2. **设置合适的面积阈值**：避免过度细分
3. **优化LOD阈值**：根据实际需求调整屏幕误差和距离阈值
4. **启用视锥体剔除**：减少不必要的渲染
5. **监控更新频率**：根据性能需求调整`update_interval`

## 扩展功能

### 自定义LOD瓦片

```python
from lod.lod_scheduler import LODTile, BoundingBox, LODLevel

# 创建自定义LOD瓦片
custom_tile = LODTile(
    id="/World/CustomTile",
    bounding_box=BoundingBox(Gf.Vec3f(-100, -100, -100), Gf.Vec3f(100, 100, 100)),
    lod_level=LODLevel.HIGH,
    usdz_path="/path/to/custom.usdz",
    screen_error=1.0,
    distance_threshold=50.0
)

# 添加到调度器
scheduler.lod_tiles[custom_tile.id] = custom_tile
```

### 自定义配置

```python
from lod.lod_config import create_custom_config

# 创建自定义配置
custom_config = create_custom_config(
    screen_error_thresholds={
        "high": 0.5,
        "medium": 2.0,
        "low": 8.0,
        "very_low": 25.0
    },
    distance_thresholds={
        "high": 25.0,
        "medium": 75.0,
        "low": 150.0,
        "very_low": 300.0
    },
    max_depth=8,
    min_area_threshold=50.0
)
```

## 故障排除

### 常见问题

1. **相机未找到**：确保相机路径正确，相机存在于场景中
2. **边界框读取失败**：检查prim是否包含`omni:nurec:crop:minBounds`和`omni:nurec:crop:maxBounds`属性
3. **性能问题**：调整LOD阈值和八叉树参数
4. **内存不足**：减少八叉树深度或增加面积阈值

### 调试模式

启用调试模式获取详细信息：

```python
scheduler = LODScheduler(stage, camera_path="/World/Camera")
# 在相关方法中启用调试输出
scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=100.0)
```

## 文件结构

```
lod/
├── lod_scheduler.py      # 主要的LOD调度器
├── lod_config.py         # 配置管理
├── test_lod_scheduler.py # 测试脚本
└── README.md            # 本文档
```

## 依赖项

- USD (Pixar Universal Scene Description)
- omni.usd
- Python 3.7+
- 标准库：math, json, typing, dataclasses, enum

## 许可证

本项目遵循MIT许可证。 