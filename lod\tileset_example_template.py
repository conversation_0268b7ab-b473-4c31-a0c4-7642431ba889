"""
Tileset示例模板类
提供标准化的配置和运行流程，简化示例脚本
"""

import os
import time
from typing import Optional, Tuple
from pxr import Gf
import omni.usd
import omni.timeline

from lod_scheduler import LODScheduler, TilesetLODManager
from lod_config import create_lod_config_from_tileset


class TilesetConfig:
    """Tileset LOD配置类"""
    def __init__(self):
        # 路径配置 - 需要用户修改
        self.tileset_path = "E:/wanleqi/isaacsim-python-scripts/lod/tileset_data/florenz_village/hierarchy/tileset_hierarchy.json"
        self.usd_file_path = "C:/test-usd-path"

        # 相机配置
        self.camera_path = "/World/Camera"

        # LOD切换配置
        self.auto_mode = "movement"  # "movement" 或 "timer"
        self.timer_interval = 1.0  # 主线程事件订阅间隔（秒）
        self.debug_info = True  # 是否输出调试信息

        # 运行时控制
        self.auto_start_runtime = True  # 是否自动开启运行时

        # 相机移动模拟配置
        self.camera_start_position = Gf.Vec3f(0, 0, 50)  # 相机起始位置
        self.camera_target_position = Gf.Vec3f(20, 0, 100)  # 相机目标位置
        self.camera_movement_duration = 30.0  # 移动持续时间（秒）
        self.camera_movement_loop = True  # 是否循环移动

        # 分批显示配置
        self.enable_batch_display = True  # 是否启用分批显示（按tile_index分组）
        self.batch_interval = 0.15  # 批次间隔（秒）- 每个tile_index组之间的间隔

        # 使用中心化LOD配置
        self.lod_config = create_lod_config_from_tileset(self.tileset_path)

        # 从中心化配置获取SSE配置
        self.maximum_screen_space_error = self.lod_config.maximum_screen_space_error
        self.screen_width = self.lod_config.screen_width
        self.horizontal_fov = self.lod_config.horizontal_fov


class TilesetExampleTemplate:
    """Tileset示例模板类"""
    
    def __init__(self, config: TilesetConfig = None):
        """
        初始化示例模板
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or TilesetConfig()
        self.stage = None
        self.lod_scheduler = None
        self.tileset_manager = None
        self.region_bounds = None
        
    def validate_paths(self) -> bool:
        """验证路径配置"""
        if not self.config.tileset_path or not os.path.exists(self.config.tileset_path):
            print(f"ERROR: Tileset file not found: {self.config.tileset_path}")
            return False
            
        if not self.config.usd_file_path or not os.path.exists(self.config.usd_file_path):
            print(f"ERROR: USD file not found: {self.config.usd_file_path}")
            return False
            
        return True
    
    def load_usd_stage(self) -> bool:
        """加载USD文件到stage"""
        try:
            print(f"Loading USD file: {self.config.usd_file_path}")
            
            omni.usd.get_context().open_stage(self.config.usd_file_path)
            self.stage = omni.usd.get_context().get_stage()

            if not self.stage:
                print(f"ERROR: Failed to open USD file: {self.config.usd_file_path}")
                return False

            print(f"Successfully loaded USD file: {self.config.usd_file_path}")
            return True

        except Exception as e:
            print(f"ERROR: Failed to load USD file: {e}")
            return False
    
    def wait_for_render_initialization(self, max_wait_time: float = 10.0) -> bool:
        """等待渲染系统初始化完成"""
        print("Waiting for render system initialization...")

        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            # 检查是否有可用的 stage
            if self.stage:
                # 检查是否有 tileset prims
                tileset_prims = []
                for prim in self.stage.Traverse():
                    # 检查tileset属性
                    attr_depth = prim.GetAttribute("tileset:depth")
                    attr_geom_error = prim.GetAttribute("tileset:geometricError")

                    # 或者检查旧的nurec属性（向后兼容）
                    attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
                    attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")

                    if (attr_depth and attr_geom_error) or (attr_min and attr_max):
                        tileset_prims.append(prim)

                if tileset_prims:
                    print(f"Found {len(tileset_prims)} tileset prim(s), render system ready")
                    return True
                else:
                    print("Stage loaded but no tileset prims found, continuing to wait...")

            time.sleep(0.1)  # 短暂等待

        print(f"Warning: Render initialization timeout after {max_wait_time}s")
        return False
    
    def start_runtime(self) -> bool:
        """启动运行时（播放模式）"""
        try:
            timeline = omni.timeline.get_timeline_interface()
            timeline.play()
            print("Runtime started (Play mode)")
            return True
        except Exception as e:
            print(f"ERROR: Failed to start runtime: {e}")
            return False
    
    def setup_lod_system(self) -> bool:
        """设置LOD系统"""
        try:
            # 创建LOD调度器
            self.lod_scheduler = LODScheduler(
                self.stage, 
                camera_path=self.config.camera_path, 
                centralized_config=self.config.lod_config
            )
            
            # 从tileset构建八叉树
            self.lod_scheduler.build_octree_from_tileset(self.config.tileset_path)
            
            # 创建Tileset管理器
            self.tileset_manager = TilesetLODManager(
                self.stage,
                camera_path=self.config.camera_path,
                lod_scheduler=self.lod_scheduler,
                config=self.config
            )
            
            # 获取区域边界
            self.region_bounds = self.tileset_manager.get_tileset_region_bounds()
            if not self.region_bounds:
                print("ERROR: Could not find tileset region bounds")
                return False
                
            print("✅ LOD system setup completed")
            return True
            
        except Exception as e:
            print(f"ERROR: Failed to setup LOD system: {e}")
            return False
    
    def start_automatic_lod_switching(self) -> bool:
        """启动自动LOD切换"""
        if not self.tileset_manager:
            print("ERROR: Tileset manager not initialized")
            return False
            
        return self.tileset_manager.start_automatic_lod_switching(self.config.timer_interval)
    
    def stop_automatic_lod_switching(self) -> bool:
        """停止自动LOD切换"""
        if not self.tileset_manager:
            return True
            
        return self.tileset_manager.stop_automatic_lod_switching()
    
    def manual_lod_update(self) -> Tuple[Optional[str], Optional[float], Optional[float]]:
        """手动更新LOD（用于测试）"""
        if not self.tileset_manager:
            print("ERROR: Tileset manager not initialized")
            return None, None, None
            
        return self.tileset_manager.update_tileset_lod_visibility(verbose=self.config.debug_info)
    
    def get_status(self) -> dict:
        """获取当前状态"""
        status = {
            'stage_loaded': self.stage is not None,
            'lod_scheduler_ready': self.lod_scheduler is not None,
            'tileset_manager_ready': self.tileset_manager is not None,
            'region_bounds_found': self.region_bounds is not None,
            'config': {
                'tileset_path': self.config.tileset_path,
                'usd_file_path': self.config.usd_file_path,
                'camera_path': self.config.camera_path,
                'timer_interval': self.config.timer_interval,
                'enable_batch_display': self.config.enable_batch_display,
                'batch_interval': self.config.batch_interval
            }
        }
        
        if self.tileset_manager:
            status.update(self.tileset_manager.get_status())
            
        return status
    
    def run_complete_example(self) -> bool:
        """运行完整的示例流程"""
        print("=== Running Complete Tileset LOD Example ===")
        
        # 1. 验证路径
        if not self.validate_paths():
            return False
        
        # 2. 加载USD文件
        if not self.load_usd_stage():
            return False
        
        # 3. 等待渲染系统初始化
        if not self.wait_for_render_initialization():
            print("WARNING: Render initialization may not be complete")
        
        # 4. 自动启动运行时（如果配置启用）
        if self.config.auto_start_runtime:
            if not self.start_runtime():
                print("WARNING: Failed to start runtime, continuing...")
        
        # 5. 设置LOD系统
        if not self.setup_lod_system():
            return False
        
        # 6. 启动自动LOD切换
        if not self.start_automatic_lod_switching():
            return False
        
        print("✅ Complete tileset LOD example setup finished successfully!")
        return True
