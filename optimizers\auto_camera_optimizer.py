from pxr import Usd, UsdGeom, Gf
import omni.usd
import omni.kit.app
import omni.kit.commands
import omni.timeline
import omni.ext
import asyncio
import time
import threading
import math
import sys
sys.path.append("E:/wanleqi/isaacsim-python-scripts/optimizers")

# 导入camera_distance_optimizer中的方法
from camera_distance_optimizer import (
    NurecBoundsCache,
    _global_bounds_cache,
    get_camera_position_and_transform,
    get_camera_forward_direction,
    calculate_distance,
    is_camera_inside_bbox,
    calculate_distance_to_bbox,
    ray_bbox_intersection,
    calculate_ray_distance_to_bbox,
    transform_point,
    apply_coordinate_system_correction,
    optimize_by_camera_distance,
    clear_bounds_cache,
    get_cache_stats,
    force_update_cache
)

class AutoCameraOptimizer:
    """
    自动相机优化器，支持相机移动检测和定时触发
    """
    
    def __init__(self, 
                 camera_path="/World/Camera",
                 distance_threshold=50.0,
                 fix_coordinate_system=True,
                 debug_info=False,
                 auto_mode="movement",  # "movement" 或 "timer"
                 timer_interval=5.0,    # 定时器间隔（秒）
                 movement_threshold=1.0, # 相机移动检测阈值（米）
                 enabled=True,
                 runtime_auto_control=True,
                 max_blocks=2, # 新增max_blocks参数
                 use_ray_distance=True, # 是否使用射线距离检测
                 use_cache=True): # 是否使用全局缓存
        """
        初始化自动相机优化器
        
        Args:
            camera_path: 相机prim路径
            distance_threshold: 距离阈值（米）
            fix_coordinate_system: 是否应用坐标系修正
            debug_info: 是否输出调试信息
            auto_mode: 自动模式 ("movement" 或 "timer")
            timer_interval: 定时器间隔（秒）
            movement_threshold: 相机移动检测阈值（米）
            enabled: 是否启用自动优化
                    max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
        use_cache: 是否使用全局缓存（默认True）
        """
        self.camera_path = camera_path
        self.distance_threshold = distance_threshold
        self.fix_coordinate_system = fix_coordinate_system
        self.debug_info = debug_info
        self.auto_mode = auto_mode
        self.timer_interval = timer_interval
        self.movement_threshold = movement_threshold
        self.enabled = enabled
        self.runtime_auto_control = runtime_auto_control
        self.max_blocks = max_blocks # 新增max_blocks属性
        self.use_ray_distance = use_ray_distance # 新增use_ray_distance属性
        self.use_cache = use_cache # 新增use_cache属性
        
        # 内部状态
        self.last_camera_position = None
        self.last_optimization_time = 0
        self.is_running = False
        self.timer_task = None
        self.movement_subscription = None
        self._timeline = None
        self._timeline_subscription = None
        self._update_subscription = None
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None
        
        # 获取stage
        self.stage = omni.usd.get_context().get_stage()
        
        print(f"AutoCameraOptimizer initialized:")
        print(f"  Camera path: {camera_path}")
        print(f"  Distance threshold: {distance_threshold}m")
        print(f"  Auto mode: {auto_mode}")
        print(f"  Timer interval: {timer_interval}s")
        print(f"  Movement threshold: {movement_threshold}m")
        print(f"  Enabled: {enabled}")
        print(f"  Runtime auto control: {runtime_auto_control}")
        print(f"  Max blocks: {max_blocks}")
        print(f"  Use ray distance: {use_ray_distance}")
        print(f"  Use cache: {use_cache}")

        # 绑定运行时（播放/停止）监听
        if self.runtime_auto_control:
            try:
                self._timeline = omni.timeline.get_timeline_interface()
                event_stream = self._timeline.get_timeline_event_stream()
                self._timeline_subscription = event_stream.create_subscription_to_pop(
                    self._on_timeline_event,
                    name="AutoCameraOptimizerRuntimeListener"
                )
                print("Runtime listener attached (PLAY -> start, STOP -> stop)")
            except Exception as e:
                print(f"Failed to attach runtime listener: {e}")
    
    def start(self):
        """启动自动优化"""
        if self.is_running:
            print("AutoCameraOptimizer is already running")
            return
        # 仅在Play模式下允许启动
        if not self._is_playing():
            print("INFO: Timeline not playing; optimizer will not start")
            return
        
        # 检查是否是从暂停状态恢复
        if hasattr(self, '_was_paused') and self._was_paused:
            print("Resuming from pause state...")
            self._was_paused = False
            self._resume_optimizer()
            return
        
        self.is_running = True
        print(f"Starting AutoCameraOptimizer in {self.auto_mode} mode")
        
        # 在初始进入运行模式后未移动时加一次初次检测
        print("--- Initial optimization triggered on startup ---")
        self._run_optimization()
        
        # 强制更新缓存，确保缓存状态一致
        force_update_cache(self.stage)
        print("Cache force updated on start")

        if self.auto_mode == "timer":
            self._start_timer_mode()
        elif self.auto_mode == "movement":
            self._start_movement_mode()
        else:
            print(f"Unknown auto mode: {self.auto_mode}")
            self.is_running = False
    
    def stop(self):
        """停止自动优化"""
        if not self.is_running:
            print("AutoCameraOptimizer is not running")
            return
        
        self.is_running = False
        print("Stopping AutoCameraOptimizer")
        
        # 清除暂停状态标记
        self._was_paused = False
        
        # 清除全局缓存，确保下次启动时缓存状态一致
        clear_bounds_cache()
        print("Global bounds cache cleared on stop")
        
        if self.timer_task:
            self.timer_task.cancel()
            self.timer_task = None
        
        if self.movement_subscription:
            self.movement_subscription = None
        if self._update_subscription:
            try:
                self._update_subscription.unsubscribe()
            except Exception:
                pass
            self._update_subscription = None
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None

    def _pause_optimizer(self):
        """暂停优化器（保持缓存）"""
        if not self.is_running:
            print("AutoCameraOptimizer is not running")
            return
        
        print("Pausing AutoCameraOptimizer (keeping cache)")
        
        # 暂停更新回调，但不清除缓存
        if self._update_subscription:
            try:
                self._update_subscription.unsubscribe()
            except Exception:
                pass
            self._update_subscription = None
        
        # 重置时间累加器
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None
        
        # 标记为暂停状态（不是完全停止）
        self.is_running = False
        self._was_paused = True  # 标记为暂停状态

    def _resume_optimizer(self):
        """恢复优化器"""
        if self.is_running:
            print("AutoCameraOptimizer is already running")
            return
        
        # 仅在Play模式下允许恢复
        if not self._is_playing():
            print("INFO: Timeline not playing; optimizer will not resume")
            return
        
        self.is_running = True
        print(f"Resuming AutoCameraOptimizer in {self.auto_mode} mode")
        
        # 重新启动相应的模式
        if self.auto_mode == "timer":
            self._start_timer_mode()
        elif self.auto_mode == "movement":
            self._start_movement_mode()
        else:
            print(f"Unknown auto mode: {self.auto_mode}")
            self.is_running = False

    def dispose(self):
        """清理监听与资源（不影响下次进入运行时自动触发的行为外部可控）"""
        try:
            self.stop()
        except Exception:
            pass
        if self._timeline_subscription:
            self._timeline_subscription = None
        self._timeline = None

    def _on_timeline_event(self, event):
        """运行时进入/退出事件回调"""
        try:
            evt_type = getattr(event, "type", None)
            # 兼容不同版本：枚举通常为 omni.timeline.TimelineEventType
            play_type = int(getattr(omni.timeline.TimelineEventType, "PLAY", 0))
            pause_type = int(getattr(omni.timeline.TimelineEventType, "PAUSE", 1))
            stop_type = int(getattr(omni.timeline.TimelineEventType, "STOP", 2))

            if evt_type == play_type:
                if self.enabled and not self.is_running:
                    print("[Runtime] PLAY detected -> starting AutoCameraOptimizer")
                    self.start()
            elif evt_type == pause_type:
                if self.is_running:
                    print("[Runtime] PAUSE detected -> pausing AutoCameraOptimizer (keeping cache)")
                    self._pause_optimizer()
            elif evt_type == stop_type:
                if self.is_running:
                    print("[Runtime] STOP detected -> stopping AutoCameraOptimizer")
                    self.stop()
        except Exception as e:
            print(f"Error handling runtime event: {e}")

    # 移除对 asyncio 事件循环的依赖，改为使用每帧更新回调

    def _is_playing(self) -> bool:
        """检测当前是否处于播放(Play)模式。"""
        try:
            tl = self._timeline or omni.timeline.get_timeline_interface()
            for attr in ("is_playing", "get_is_playing", "IsPlaying"):
                fn = getattr(tl, attr, None)
                if callable(fn):
                    return bool(fn())
            # 兼容通过状态枚举判断
            state = getattr(tl, "get_state", lambda: None)()
            play_state = getattr(omni.timeline.TimelineEventType, "PLAY", None)
            return state == play_state
        except Exception:
            return False
    
    def _start_timer_mode(self):
        """启动定时器模式"""
        print(f"Starting timer mode with {self.timer_interval}s interval")
        if not self._is_playing():
            print("INFO: Timeline not playing; timer mode will not start")
            return
        app = omni.kit.app.get_app()
        stream = app.get_update_event_stream()
        self._last_update_ts = time.time()
        self._timer_accumulator = 0.0
        self._update_subscription = stream.create_subscription_to_pop(self._on_update_timer, name="AutoCameraOptimizerTimerUpdate")
    
    def _start_movement_mode(self):
        """启动相机移动检测模式"""
        print(f"Starting movement detection mode with {self.movement_threshold}m threshold")
        if not self._is_playing():
            print("INFO: Timeline not playing; movement detection will not start")
            return
        # 获取初始相机位置
        camera = self.stage.GetPrimAtPath(self.camera_path)
        if camera:
            position, _ = get_camera_position_and_transform(camera)
            if position:
                self.last_camera_position = position
                print(f"Initial camera position: {position}")
        
        # 启动移动检测回调
        app = omni.kit.app.get_app()
        stream = app.get_update_event_stream()
        self._last_update_ts = time.time()
        self._movement_check_accumulator = 0.0
        self._update_subscription = stream.create_subscription_to_pop(self._on_update_movement, name="AutoCameraOptimizerMovementUpdate")

    def _on_update_timer(self, e):
        if not self.is_running or not self.enabled:
            return
        now = time.time()
        dt = now - (self._last_update_ts or now)
        self._last_update_ts = now
        self._timer_accumulator += dt
        if self._timer_accumulator >= self.timer_interval:
            self._timer_accumulator = 0.0
            print(f"\n--- Timer triggered optimization ({self.timer_interval}s interval) ---")
            self._run_optimization()

    def _on_update_movement(self, e):
        if not self.is_running or not self.enabled:
            return
        now = time.time()
        dt = now - (self._last_update_ts or now)
        self._last_update_ts = now
        self._movement_check_accumulator += dt
        # 每0.5秒检查一次相机位置
        if self._movement_check_accumulator < 0.5:
            return
        self._movement_check_accumulator = 0.0

        camera = self.stage.GetPrimAtPath(self.camera_path)
        if not camera:
            return
        position, _ = get_camera_position_and_transform(camera)
        if not position:
            return
        if self.last_camera_position is not None:
            distance = self._calculate_distance(self.last_camera_position, position)
            if distance > self.movement_threshold:
                print(f"\n--- Camera movement detected ({distance:.2f}m > {self.movement_threshold}m) ---")
                self._run_optimization()
                self.last_camera_position = position
        else:
            self.last_camera_position = position
    
    async def _timer_loop(self):
        """定时器循环"""
        while self.is_running and self.enabled:
            try:
                await asyncio.sleep(self.timer_interval)
                if self.is_running and self.enabled:
                    print(f"\n--- Timer triggered optimization ({self.timer_interval}s interval) ---")
                    self._run_optimization()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in timer loop: {e}")
    
    async def _movement_detection_loop(self):
        """相机移动检测循环"""
        while self.is_running and self.enabled:
            try:
                await asyncio.sleep(0.5)  # 每0.5秒检查一次相机位置
                
                if not self.is_running or not self.enabled:
                    break
                
                camera = self.stage.GetPrimAtPath(self.camera_path)
                if not camera:
                    continue
                
                position, _ = get_camera_position_and_transform(camera)
                if not position:
                    continue
                
                # 检查相机是否移动
                if self.last_camera_position is not None:
                    distance = self._calculate_distance(self.last_camera_position, position)
                    if distance > self.movement_threshold:
                        print(f"\n--- Camera movement detected ({distance:.2f}m > {self.movement_threshold}m) ---")
                        self._run_optimization()
                        self.last_camera_position = position
                else:
                    self.last_camera_position = position
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in movement detection loop: {e}")
    
    def _calculate_distance(self, pos1, pos2):
        """计算两点之间的距离"""
        return math.sqrt((pos1[0] - pos2[0])**2 + 
                        (pos1[1] - pos2[1])**2 + 
                        (pos1[2] - pos2[2])**2)
    
    def _run_optimization(self):
        """运行优化函数"""
        try:
            current_time = time.time()
            if current_time - self.last_optimization_time < 0.1:  # 防止过于频繁的调用
                return
            
            self.last_optimization_time = current_time
            
            # 重新获取stage（以防stage发生变化）
            self.stage = omni.usd.get_context().get_stage()
            
            # 运行优化
            optimize_by_camera_distance(
                stage=self.stage,
                camera_path=self.camera_path,
                distance_threshold=self.distance_threshold,
                fix_coordinate_system=self.fix_coordinate_system,
                debug_info=self.debug_info,
                max_blocks=self.max_blocks, # 传递max_blocks参数
                use_ray_distance=self.use_ray_distance, # 使用配置的射线距离检测
                use_cache=self.use_cache # 使用配置的缓存设置
            )
            
        except Exception as e:
            print(f"Error running optimization: {e}")
    
    def manual_optimize(self):
        """手动触发优化"""
        print("\n--- Manual optimization triggered ---")
        self._run_optimization()
    
    def update_settings(self, **kwargs):
        """更新设置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"Updated {key}: {value}")
        
        # 如果正在运行，重启以应用新设置
        if self.is_running:
            self.stop()
            self.start()
    
    def get_status(self):
        """获取当前状态"""
        return {
            "is_running": self.is_running,
            "enabled": self.enabled,
            "auto_mode": self.auto_mode,
            "timer_interval": self.timer_interval,
            "movement_threshold": self.movement_threshold,
            "distance_threshold": self.distance_threshold,
            "last_camera_position": self.last_camera_position,
            "last_optimization_time": self.last_optimization_time,
            "max_blocks": self.max_blocks, # 新增max_blocks状态
            "use_ray_distance": self.use_ray_distance, # 新增use_ray_distance状态
            "use_cache": self.use_cache # 新增use_cache状态
        }

# 全局优化器实例
_optimizer_instance = None

def create_auto_optimizer(camera_path="/World/Camera",
                         distance_threshold=50.0,
                         fix_coordinate_system=True,
                         debug_info=False,
                         auto_mode="movement",
                         timer_interval=5.0,
                         movement_threshold=1.0,
                         enabled=True,
                         max_blocks=3, # 修改默认max_blocks为3
                         use_ray_distance=False, # 修改默认use_ray_distance为False
                         use_cache=True): # 是否使用全局缓存
    """
    创建并返回自动相机优化器实例
    
    Args:
        camera_path: 相机prim路径
        distance_threshold: 距离阈值（米）
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
        auto_mode: 自动模式 ("movement" 或 "timer")
        timer_interval: 定时器间隔（秒）
        movement_threshold: 相机移动检测阈值（米）
        enabled: 是否启用自动优化
        max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
    
    Returns:
        AutoCameraOptimizer实例
    """
    global _optimizer_instance
    
    # 如果已有实例，先停止
    if _optimizer_instance:
        _optimizer_instance.stop()
    
    # 创建新实例
    _optimizer_instance = AutoCameraOptimizer(
        camera_path=camera_path,
        distance_threshold=distance_threshold,
        fix_coordinate_system=fix_coordinate_system,
        debug_info=debug_info,
        auto_mode=auto_mode,
        timer_interval=timer_interval,
        movement_threshold=movement_threshold,
        enabled=enabled,
        max_blocks=max_blocks, # 传递max_blocks参数
        use_ray_distance=use_ray_distance, # 传递use_ray_distance参数
        use_cache=use_cache # 传递use_cache参数
    )
    
    return _optimizer_instance

def start_auto_optimization(camera_path="/World/Camera",
                          distance_threshold=50.0,
                          fix_coordinate_system=True,
                          debug_info=False,
                          auto_mode="movement",
                          timer_interval=5.0,
                          movement_threshold=1.0,
                          max_blocks=3, # 修改默认max_blocks为3
                          use_ray_distance=False, # 修改默认use_ray_distance为False
                          use_cache=True): # 是否使用全局缓存
    """
    启动自动相机优化
    
    Args:
        camera_path: 相机prim路径
        distance_threshold: 距离阈值（米）
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
        auto_mode: 自动模式 ("movement" 或 "timer")
        timer_interval: 定时器间隔（秒）
        movement_threshold: 相机移动检测阈值（米）
        max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
        use_cache: 是否使用全局缓存（默认True）
    
    Returns:
        AutoCameraOptimizer实例
    """
    optimizer = create_auto_optimizer(
        camera_path=camera_path,
        distance_threshold=distance_threshold,
        fix_coordinate_system=fix_coordinate_system,
        debug_info=debug_info,
        auto_mode=auto_mode,
        timer_interval=timer_interval,
        movement_threshold=movement_threshold,
        enabled=True,
        max_blocks=max_blocks, # 传递max_blocks参数
        use_ray_distance=use_ray_distance, # 传递use_ray_distance参数
        use_cache=use_cache # 传递use_cache参数
    )
    
    optimizer.start()
    return optimizer

def stop_auto_optimization():
    """停止自动相机优化"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.stop()
        return True
    return False

def manual_optimize():
    """手动触发优化"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.manual_optimize()
        return True
    return False

def get_optimizer_status():
    """获取优化器状态"""
    global _optimizer_instance
    if _optimizer_instance:
        return _optimizer_instance.get_status()
    return None

# 示例使用
if __name__ == "__main__":
    print("=== Auto Camera Optimizer Demo ===")
    
    # 示例1: 启动相机移动检测模式
    print("\n1. Starting movement detection mode...")
    optimizer1 = start_auto_optimization(
        camera_path="/World/Camera",
        distance_threshold=50.0,
        auto_mode="movement",
        movement_threshold=2.0,  # 相机移动2米以上时触发
        debug_info=True
    )
    
    # 示例2: 启动定时器模式
    # print("\n2. Starting timer mode...")
    # optimizer2 = start_auto_optimization(
    #     camera_path="/World/Camera",
    #     distance_threshold=50.0,
    #     auto_mode="timer",
    #     timer_interval=10.0,  # 每10秒触发一次
    #     debug_info=True
    # )
    
    # 示例3: 手动触发优化
    # print("\n3. Manual optimization...")
    # manual_optimize()
    
    # 示例4: 获取状态
    # print("\n4. Getting status...")
    # status = get_optimizer_status()
    # print(f"Status: {status}")
    
    # 示例5: 停止优化
    # print("\n5. Stopping optimization...")
    # stop_auto_optimization() 