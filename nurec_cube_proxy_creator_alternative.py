from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import omni.kit.commands
import omni.kit.app
import math
from omni.isaac.core.utils.prims import set_targets

def transform_point(p, transform_matrix):
    """
    变换点的函数，应用坐标系修正
    """
    point4 = Gf.Vec4d(p[0], p[1], p[2], 1.0)
    transformed = transform_matrix * point4
    return Gf.Vec3f(transformed[0], transformed[1], transformed[2])

def apply_coordinate_system_correction(minB, maxB, debug_info=False):
    """
    应用坐标系修正到边界框
    """
    if debug_info:
        print(f"  Original bounds: {minB} to {maxB}")
    
    # 应用 (90, 180, 0) 旋转的逆变换到坐标点
    rot_z_180 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(0, 0, 1), -180))
    rot_x_90 = Gf.Matrix4d().SetRotate(Gf.Rotation(Gf.Vec3d(1, 0, 0), 90))
    transform_matrix = rot_z_180 * rot_x_90

    # 重新计算变换后的最小最大值
    all_transformed_points = [
        transform_point(Gf.Vec3f(minB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], minB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], minB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(maxB[0], maxB[1], maxB[2]), transform_matrix),
        transform_point(Gf.Vec3f(minB[0], maxB[1], maxB[2]), transform_matrix)
    ]

    # 计算变换后的实际边界
    min_x = min(p[0] for p in all_transformed_points)
    max_x = max(p[0] for p in all_transformed_points)
    min_y = min(p[1] for p in all_transformed_points)
    max_y = max(p[1] for p in all_transformed_points)
    min_z = min(p[2] for p in all_transformed_points)
    max_z = max(p[2] for p in all_transformed_points)

    corrected_minB = Gf.Vec3f(min_x, min_y, min_z)
    corrected_maxB = Gf.Vec3f(max_x, max_y, max_z)
    
    if debug_info:
        print(f"  Corrected bounds: {corrected_minB} to {corrected_maxB}")
    
    return corrected_minB, corrected_maxB

def create_cube_for_nurec_bounds_alternative(stage, fix_coordinate_system=True, debug_info=False):
    """
    为stage中的每个nurec prim的父节点创建Cube，并设置proxy target
    使用替代方法避免XformOp精度冲突
    
    Args:
        stage: USD stage
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
    """
    print(">> Starting nurec cube proxy creation (alternative method)")
    if fix_coordinate_system:
        print("  Applying coordinate system correction (90, 180, 0) rotation")
    else:
        print("  No coordinate system correction applied")
    
    total_nurec_prims = 0
    created_cubes = 0
    set_proxy_targets = 0
    
    for prim in stage.Traverse():
        # 查找具有crop bounds的nurec prim
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue
        
        total_nurec_prims += 1
        if debug_info:
            print(f"Found nurec prim: {prim.GetPath()}")
        
        try:
            minB = Gf.Vec3f(*attr_min.Get())
            maxB = Gf.Vec3f(*attr_max.Get())
            if debug_info:
                print(f"  Original bounds: min={minB}, max={maxB}")
        except Exception as e:
            print(f"  ERR reading bounds for {prim.GetPath()}: {e}")
            continue

        # 获取父节点
        parent = prim.GetParent()
        if not parent or not parent.IsValid():
            print(f"  WARNING: parent invalid for {prim.GetPath()}")
            continue
        
        # 应用坐标系修正到边界框
        if fix_coordinate_system:
            minB, maxB = apply_coordinate_system_correction(minB, maxB, debug_info)
        
        # 计算Cube的尺寸和中心位置
        size = maxB - minB
        center = (minB + maxB) * 0.5
        
        if debug_info:
            print(f"  Parent: {parent.GetPath()}")
            print(f"  Cube size: {size}")
            print(f"  Cube center: {center}")
        
        # 创建Cube
        cube_name = f"nurec_cube_{total_nurec_prims}"
        cube_path = f"{parent.GetPath()}/{cube_name}"
        
        try:
            # 使用omni.kit.commands创建Cube
            omni.kit.commands.execute(
                "CreatePrimWithDefaultXform",
                prim_type="Cube",
                prim_path=cube_path
            )
            
            # 获取创建的Cube prim
            cube_prim = stage.GetPrimAtPath(cube_path)
            if not cube_prim:
                print(f"  ERROR: Failed to create cube at {cube_path}")
                continue
            
            # 使用替代方法设置Cube的变换
            try:
                xformable = UsdGeom.Xformable(cube_prim)
                if xformable:
                    # 创建变换矩阵 - 先缩放再平移
                    scale_matrix = Gf.Matrix4d().SetScale(Gf.Vec3d(size[0]/2.0, 0, size[2]/2.0))
                    translate_matrix = Gf.Matrix4d().SetTranslate(Gf.Vec3d(center[0], center[1], center[2]))
                    transform_matrix = scale_matrix * translate_matrix
                    
                    # 清除现有的变换操作
                    xformable.ClearXformOpOrder()
                    
                    # 添加矩阵变换操作
                    xformable.AddTransformOp().Set(transform_matrix)
                    if debug_info:
                        print(f"  Set cube transform matrix")
            except Exception as matrix_error:
                print(f"  ERROR: Matrix transform also failed: {matrix_error}")
                continue
            
            created_cubes += 1
            
            # 设置proxy target
            set_targets(
                prim=stage.GetPrimAtPath(prim.GetPath()),
                attribute="proxy",
                target_prim_paths=[cube_path]
            )
            
        except Exception as e:
            print(f"  ERROR creating cube for {prim.GetPath()}: {e}")
            continue
    
    print(f"Total nurec prims found: {total_nurec_prims}")
    print(f"Cubes created: {created_cubes}")
    print(f"Proxy targets set: {set_proxy_targets}")
    print(">> nurec cube proxy creation completed")

def main():
    """主函数"""
    print("=== NUREC Cube Proxy Creator (Alternative Method) ===")
    
    # 获取当前stage
    stage = omni.usd.get_context().get_stage()
    if not stage:
        print("ERROR: No USD stage available")
        return
    
    print(f"Current stage: {stage.GetRootLayer().identifier}")
    
    # 使用替代方法为所有nurec prim创建Cube
    print("\nCreating cubes for all nurec prims (alternative method)...")
    create_cube_for_nurec_bounds_alternative(stage, fix_coordinate_system=True, debug_info=True)
    
    print("\nDone! Check the stage to see the created cubes.")

if __name__ == "__main__":
    main() 