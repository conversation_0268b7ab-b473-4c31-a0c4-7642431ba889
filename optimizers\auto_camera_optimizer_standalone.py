
import asyncio
import time
import math
import sys
import os

# Isaac Sim standalone 支持
from isaacsim import SimulationApp
# HUD 配置标志位
DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
    "anti_aliasing": 0,
}

simulation_app = SimulationApp(launch_config=config)


# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入camera_distance_optimizer中的方法
try:
    from camera_distance_optimizer import (
        NurecBoundsCache,
        _global_bounds_cache,
        get_camera_position_and_transform,
        get_camera_forward_direction,
        calculate_distance,
        is_camera_inside_bbox,
        calculate_distance_to_bbox,
        ray_bbox_intersection,
        calculate_ray_distance_to_bbox,
        transform_point,
        apply_coordinate_system_correction,
        optimize_by_camera_distance,
        clear_bounds_cache,
        get_cache_stats,
        force_update_cache
    )
    print("✓ Successfully imported camera_distance_optimizer functions")
except ImportError as e:
    print(f"⚠️ Warning: Could not import camera_distance_optimizer functions: {e}")
    print("Will use built-in functions instead")
    
    # 如果导入失败，使用内置函数（这里需要添加完整的函数实现）
    # 为了简化，我们暂时跳过这些函数的定义
    pass

class AutoCameraOptimizer:
    """
    自动相机优化器，支持相机移动检测和定时触发
    """
    
    def __init__(self, 
                 camera_path="/World/Camera",
                 distance_threshold=50.0,
                 fix_coordinate_system=True,
                 debug_info=False,
                 auto_mode="movement",  # "movement" 或 "timer"
                 timer_interval=5.0,    # 定时器间隔（秒）
                 movement_threshold=1.0, # 相机移动检测阈值（米）
                 rotation_threshold=45.0, # y轴旋转角度检测阈值（度）
                 detection_interval=2.0, # 检测间隔（秒）
                 enabled=True,
                 runtime_auto_control=True,
                 max_blocks=2, # 新增max_blocks参数
                 use_ray_distance=True, # 是否使用射线距离检测
                 use_cache=True): # 是否使用全局缓存
        """
        初始化自动相机优化器
        
        Args:
            camera_path: 相机prim路径
            distance_threshold: 距离阈值（米）
            fix_coordinate_system: 是否应用坐标系修正
            debug_info: 是否输出调试信息
            auto_mode: 自动模式 ("movement" 或 "timer")
            timer_interval: 定时器间隔（秒）
            movement_threshold: 相机移动检测阈值（米）
            enabled: 是否启用自动优化
                    max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
        use_cache: 是否使用全局缓存（默认True）
        """
        self.camera_path = camera_path
        self.distance_threshold = distance_threshold
        self.fix_coordinate_system = fix_coordinate_system
        self.debug_info = debug_info
        self.auto_mode = auto_mode
        self.timer_interval = timer_interval
        self.movement_threshold = movement_threshold
        self.rotation_threshold = rotation_threshold  # 新增rotation_threshold属性
        self.detection_interval = detection_interval  # 新增detection_interval属性
        self.enabled = enabled
        self.runtime_auto_control = runtime_auto_control
        self.max_blocks = max_blocks # 新增max_blocks属性
        self.use_ray_distance = use_ray_distance # 新增use_ray_distance属性
        self.use_cache = use_cache # 新增use_cache属性
        
        # 内部状态
        self.last_camera_position = None
        self.last_camera_rotation_y = None  # 添加y轴旋转角度记录
        self.accumulated_rotation_diff = 0.0  # 累积旋转差值
        self.last_optimization_time = 0
        self.is_running = False
        self.timer_task = None
        self.movement_subscription = None
        self._timeline = None
        self._timeline_subscription = None
        self._update_subscription = None
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None
        
        # 获取stage
        self.stage = omni.usd.get_context().get_stage()
        
        print(f"AutoCameraOptimizer initialized:")
        print(f"  Camera path: {camera_path}")
        print(f"  Distance threshold: {distance_threshold}m")
        print(f"  Auto mode: {auto_mode}")
        print(f"  Timer interval: {timer_interval}s")
        print(f"  Movement threshold: {movement_threshold}m")
        print(f"  Rotation threshold: {rotation_threshold}°")
        print(f"  Detection interval: {detection_interval}s")
        print(f"  Enabled: {enabled}")
        print(f"  Runtime auto control: {runtime_auto_control}")
        print(f"  Max blocks: {max_blocks}")
        print(f"  Use ray distance: {use_ray_distance}")
        print(f"  Use cache: {use_cache}")

        # 绑定运行时（播放/停止）监听
        if self.runtime_auto_control:
            try:
                self._timeline = omni.timeline.get_timeline_interface()
                event_stream = self._timeline.get_timeline_event_stream()
                self._timeline_subscription = event_stream.create_subscription_to_pop(
                    self._on_timeline_event,
                    name="AutoCameraOptimizerRuntimeListener"
                )
                print("Runtime listener attached (PLAY -> start, STOP -> stop)")
            except Exception as e:
                print(f"Failed to attach runtime listener: {e}")
    
    def start(self):
        """启动自动优化"""
        if self.is_running:
            print("AutoCameraOptimizer is already running")
            return
        # 仅在Play模式下允许启动
        if not self._is_playing():
            print("INFO: Timeline not playing; optimizer will not start")
            return
        
        # 检查是否是从暂停状态恢复
        if hasattr(self, '_was_paused') and self._was_paused:
            print("Resuming from pause state...")
            self._was_paused = False
            self._resume_optimizer()
            return
        
        self.is_running = True
        print(f"Starting AutoCameraOptimizer in {self.auto_mode} mode")
        
        # 在初始进入运行模式后未移动时加一次初次检测
        print("--- Initial optimization triggered on startup ---")
        self._run_optimization()
        
        # 打印初始相机状态
        # self.print_camera_status()
        
        # 强制更新缓存，确保缓存状态一致
        force_update_cache(self.stage)
        print("Cache force updated on start")

        if self.auto_mode == "timer":
            self._start_timer_mode()
        elif self.auto_mode == "movement":
            self._start_movement_mode()
        else:
            print(f"Unknown auto mode: {self.auto_mode}")
            self.is_running = False
    
    def stop(self):
        """停止自动优化"""
        if not self.is_running:
            print("AutoCameraOptimizer is not running")
            return
        
        self.is_running = False
        print("Stopping AutoCameraOptimizer")
        
        # 清除暂停状态标记
        self._was_paused = False
        
        # 清除全局缓存，确保下次启动时缓存状态一致
        clear_bounds_cache()
        print("Global bounds cache cleared on stop")
        
        if self.timer_task:
            self.timer_task.cancel()
            self.timer_task = None
        
        if self.movement_subscription:
            self.movement_subscription = None
        if self._update_subscription:
            try:
                self._update_subscription.unsubscribe()
            except Exception:
                pass
            self._update_subscription = None
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None

    def _pause_optimizer(self):
        """暂停优化器（保持缓存）"""
        if not self.is_running:
            print("AutoCameraOptimizer is not running")
            return
        
        print("Pausing AutoCameraOptimizer (keeping cache)")
        
        # 暂停更新回调，但不清除缓存
        if self._update_subscription:
            try:
                self._update_subscription.unsubscribe()
            except Exception:
                pass
            self._update_subscription = None
        
        # 重置时间累加器
        self._timer_accumulator = 0.0
        self._movement_check_accumulator = 0.0
        self._last_update_ts = None
        
        # 标记为暂停状态（不是完全停止）
        self.is_running = False
        self._was_paused = True  # 标记为暂停状态

    def _resume_optimizer(self):
        """恢复优化器"""
        if self.is_running:
            print("AutoCameraOptimizer is already running")
            return
        
        # 仅在Play模式下允许恢复
        if not self._is_playing():
            print("INFO: Timeline not playing; optimizer will not resume")
            return
        
        self.is_running = True
        print(f"Resuming AutoCameraOptimizer in {self.auto_mode} mode")
        
        # 重新启动相应的模式
        if self.auto_mode == "timer":
            self._start_timer_mode()
        elif self.auto_mode == "movement":
            self._start_movement_mode()
        else:
            print(f"Unknown auto mode: {self.auto_mode}")
            self.is_running = False

    def dispose(self):
        """清理监听与资源（不影响下次进入运行时自动触发的行为外部可控）"""
        try:
            self.stop()
        except Exception:
            pass
        if self._timeline_subscription:
            self._timeline_subscription = None
        self._timeline = None

    def _on_timeline_event(self, event):
        """运行时进入/退出事件回调"""
        try:
            evt_type = getattr(event, "type", None)
            # 兼容不同版本：枚举通常为 omni.timeline.TimelineEventType
            play_type = int(getattr(omni.timeline.TimelineEventType, "PLAY", 0))
            pause_type = int(getattr(omni.timeline.TimelineEventType, "PAUSE", 1))
            stop_type = int(getattr(omni.timeline.TimelineEventType, "STOP", 2))

            if evt_type == play_type:
                if self.enabled and not self.is_running:
                    print("[Runtime] PLAY detected -> starting AutoCameraOptimizer")
                    self.start()
            elif evt_type == pause_type:
                if self.is_running:
                    print("[Runtime] PAUSE detected -> pausing AutoCameraOptimizer (keeping cache)")
                    self._pause_optimizer()
            elif evt_type == stop_type:
                if self.is_running:
                    print("[Runtime] STOP detected -> stopping AutoCameraOptimizer")
                    self.stop()
        except Exception as e:
            print(f"Error handling runtime event: {e}")

    # 移除对 asyncio 事件循环的依赖，改为使用每帧更新回调

    def _is_playing(self) -> bool:
        """检测当前是否处于播放(Play)模式。"""
        try:
            tl = self._timeline or omni.timeline.get_timeline_interface()
            for attr in ("is_playing", "get_is_playing", "IsPlaying"):
                fn = getattr(tl, attr, None)
                if callable(fn):
                    return bool(fn())
            # 兼容通过状态枚举判断
            state = getattr(tl, "get_state", lambda: None)()
            play_state = getattr(omni.timeline.TimelineEventType, "PLAY", None)
            return state == play_state
        except Exception:
            return False
    
    def _start_timer_mode(self):
        """启动定时器模式"""
        print(f"Starting timer mode with {self.timer_interval}s interval")
        if not self._is_playing():
            print("INFO: Timeline not playing; timer mode will not start")
            return
        app = omni.kit.app.get_app()
        stream = app.get_update_event_stream()
        self._last_update_ts = time.time()
        self._timer_accumulator = 0.0
        self._update_subscription = stream.create_subscription_to_pop(self._on_update_timer, name="AutoCameraOptimizerTimerUpdate")
    
    def _start_movement_mode(self):
        """启动相机移动检测模式"""
        print(f"Starting movement detection mode with {self.movement_threshold}m threshold and {self.rotation_threshold}° rotation threshold")
        if not self._is_playing():
            print("INFO: Timeline not playing; movement detection will not start")
            return
        # 获取初始相机位置和旋转
        camera = self.stage.GetPrimAtPath(self.camera_path)
        if camera:
            position, transform = get_camera_position_and_transform(camera)
            if position and transform:
                self.last_camera_position = position
                self.last_camera_rotation_y = self._extract_y_rotation(transform)
                print(f"Initial camera position: {position}")
                if self.last_camera_rotation_y is not None:
                    print(f"Initial camera rotation Y: {self.last_camera_rotation_y:.2f}°")
                else:
                    print(f"Initial camera rotation Y: Unable to extract")
        
        # 启动移动检测回调
        app = omni.kit.app.get_app()
        stream = app.get_update_event_stream()
        self._last_update_ts = time.time()
        self._movement_check_accumulator = 0.0
        self._update_subscription = stream.create_subscription_to_pop(self._on_update_movement, name="AutoCameraOptimizerMovementUpdate")

    def _on_update_timer(self, e):
        if not self.is_running or not self.enabled:
            return
        now = time.time()
        dt = now - (self._last_update_ts or now)
        self._last_update_ts = now
        self._timer_accumulator += dt
        if self._timer_accumulator >= self.timer_interval:
            self._timer_accumulator = 0.0
            print(f"\n--- Timer triggered optimization ({self.timer_interval}s interval) ---")
            self._run_optimization()

    def _on_update_movement(self, e):
        if not self.is_running or not self.enabled:
            return
        now = time.time()
        dt = now - (self._last_update_ts or now)
        self._last_update_ts = now
        self._movement_check_accumulator += dt
        # 使用配置的检测间隔检查相机位置
        if self._movement_check_accumulator < self.detection_interval:
            return
        self._movement_check_accumulator = 0.0

        camera = self.stage.GetPrimAtPath(self.camera_path)
        if not camera:
            return
        position, transform = get_camera_position_and_transform(camera)
        if not position or not transform:
            return
            
        # 提取当前y轴旋转角度
        current_rotation_y = self._extract_y_rotation(transform)
        
        # 检查相机是否移动或旋转
        movement_detected = False
        rotation_detected = False
        
        if self.last_camera_position is not None:
            distance = self._calculate_distance(self.last_camera_position, position)
            if distance > self.movement_threshold:
                print(f"\n--- Camera movement detected ({distance:.2f}m > {self.movement_threshold}m) ---")
                movement_detected = True
        
        if self.last_camera_rotation_y is not None and current_rotation_y is not None:
            rotation_diff = self._calculate_rotation_difference(self.last_camera_rotation_y, current_rotation_y)
            if rotation_diff:
                # 累积旋转差值
                self.accumulated_rotation_diff += rotation_diff
                
                # 检查单次旋转或累积旋转是否超过阈值
                if rotation_diff > self.rotation_threshold:
                    print(f"\n--- Camera rotation detected (single: {rotation_diff:.2f}° > {self.rotation_threshold}°) ---")
                    print(f"   Previous rotation: {self.last_camera_rotation_y:.2f}°")
                    print(f"   Current rotation: {current_rotation_y:.2f}°")
                    print(f"   Rotation difference: {rotation_diff:.2f}°")
                    rotation_detected = True
                elif self.accumulated_rotation_diff > self.rotation_threshold:
                    print(f"\n--- Camera rotation detected (accumulated: {self.accumulated_rotation_diff:.2f}° > {self.rotation_threshold}°) ---")
                    print(f"   Previous rotation: {self.last_camera_rotation_y:.2f}°")
                    print(f"   Current rotation: {current_rotation_y:.2f}°")
                    print(f"   Single difference: {rotation_diff:.2f}°")
                    print(f"   Accumulated difference: {self.accumulated_rotation_diff:.2f}°")
                    rotation_detected = True
                else:
                    # 打印旋转信息（即使没有触发优化）- 仅在debug模式下显示
                    if self.debug_info:
                        print(f"Camera rotation: {self.last_camera_rotation_y:.2f}° -> {current_rotation_y:.2f}° (diff: {rotation_diff:.2f}°, accumulated: {self.accumulated_rotation_diff:.2f}°)")
        
        # 如果检测到移动或旋转，触发优化
        if movement_detected or rotation_detected:
            self._run_optimization()
            self.last_camera_position = position
            self.last_camera_rotation_y = current_rotation_y
            # 重置累积旋转差值
            self.accumulated_rotation_diff = 0.0
        else:
            # 更新位置和旋转记录（即使没有触发优化）
            self.last_camera_position = position
            self.last_camera_rotation_y = current_rotation_y
    
    async def _timer_loop(self):
        """定时器循环"""
        while self.is_running and self.enabled:
            try:
                await asyncio.sleep(self.timer_interval)
                if self.is_running and self.enabled:
                    print(f"\n--- Timer triggered optimization ({self.timer_interval}s interval) ---")
                    self._run_optimization()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in timer loop: {e}")
    
    async def _movement_detection_loop(self):
        """相机移动检测循环"""
        while self.is_running and self.enabled:
            try:
                await asyncio.sleep(0.5)  # 每0.5秒检查一次相机位置
                
                if not self.is_running or not self.enabled:
                    break
                
                camera = self.stage.GetPrimAtPath(self.camera_path)
                if not camera:
                    continue
                
                position, transform = get_camera_position_and_transform(camera)
                if not position or not transform:
                    continue
                
                # 提取当前y轴旋转角度
                current_rotation_y = self._extract_y_rotation(transform)
                
                # 检查相机是否移动或旋转
                movement_detected = False
                rotation_detected = False
                
                if self.last_camera_position is not None:
                    distance = self._calculate_distance(self.last_camera_position, position)
                    if distance > self.movement_threshold:
                        print(f"\n--- Camera movement detected ({distance:.2f}m > {self.movement_threshold}m) ---")
                        movement_detected = True
                
                if self.last_camera_rotation_y is not None and current_rotation_y is not None:
                    rotation_diff = self._calculate_rotation_difference(self.last_camera_rotation_y, current_rotation_y)
                    if rotation_diff and rotation_diff > self.rotation_threshold:
                        print(f"\n--- Camera rotation detected ({rotation_diff:.2f}° > {self.rotation_threshold}°) ---")
                        rotation_detected = True
                
                # 如果检测到移动或旋转，触发优化
                if movement_detected or rotation_detected:
                    self._run_optimization()
                    self.last_camera_position = position
                    self.last_camera_rotation_y = current_rotation_y
                else:
                    # 更新位置和旋转记录（即使没有触发优化）
                    self.last_camera_position = position
                    self.last_camera_rotation_y = current_rotation_y
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in movement detection loop: {e}")
    
    def _calculate_distance(self, pos1, pos2):
        """计算两点之间的距离"""
        return math.sqrt((pos1[0] - pos2[0])**2 + 
                        (pos1[1] - pos2[1])**2 + 
                        (pos1[2] - pos2[2])**2)
    
    def _extract_y_rotation(self, transform_matrix):
        """从变换矩阵中提取y轴旋转角度（度）"""
        try:
            # 使用atan2方法直接从矩阵中提取Y轴旋转角度
            # 对于Y轴旋转，我们使用矩阵的[0][2]和[2][2]元素
            y_rotation_rad = math.atan2(-transform_matrix[0][2], transform_matrix[2][2])
            
            # 转换为度
            y_rotation_deg = math.degrees(y_rotation_rad)
            
            # 确保角度在0-360范围内
            if y_rotation_deg < 0:
                y_rotation_deg += 360
                
            return y_rotation_deg
        except Exception as e:
            print(f"Error extracting y rotation: {e}")
            return None
    
    def _calculate_rotation_difference(self, angle1, angle2):
        """计算两个角度之间的最小差值（处理角度环绕）"""
        if angle1 is None or angle2 is None:
            return None
        
        diff = abs(angle2 - angle1)
        # 处理角度环绕（例如：从350度到10度，差值应该是20度而不是340度）
        if diff > 180:
            diff = 360 - diff
        return diff
    
    def _run_optimization(self):
        """运行优化函数"""
        try:
            current_time = time.time()
            if current_time - self.last_optimization_time < 0.1:  # 防止过于频繁的调用
                return
            
            self.last_optimization_time = current_time
            
            # 重新获取stage（以防stage发生变化）
            self.stage = omni.usd.get_context().get_stage()
            
            # 运行优化
            optimize_by_camera_distance(
                stage=self.stage,
                camera_path=self.camera_path,
                distance_threshold=self.distance_threshold,
                fix_coordinate_system=self.fix_coordinate_system,
                debug_info=self.debug_info,
                max_blocks=self.max_blocks, # 传递max_blocks参数
                use_ray_distance=self.use_ray_distance, # 使用配置的射线距离检测
                use_cache=self.use_cache # 使用配置的缓存设置
            )
            
        except Exception as e:
            print(f"Error running optimization: {e}")
    
    def manual_optimize(self):
        """手动触发优化"""
        print("\n--- Manual optimization triggered ---")
        self._run_optimization()
    
    def print_camera_status(self):
        """打印当前相机状态信息"""
        try:
            camera = self.stage.GetPrimAtPath(self.camera_path)
            if not camera:
                print("ERROR: Camera not found")
                return
            
            position, transform = get_camera_position_and_transform(camera)
            if not position or not transform:
                print("ERROR: Failed to get camera transform")
                return
            
            current_rotation_y = self._extract_y_rotation(transform)
            
            print(f"\n=== Camera Status ===")
            print(f"Camera path: {self.camera_path}")
            print(f"Current position: {position}")
            
            if current_rotation_y is not None:
                print(f"Current Y rotation: {current_rotation_y:.2f}°")
            else:
                print(f"Current Y rotation: Unable to extract")
            
            if self.last_camera_position is not None:
                distance = self._calculate_distance(self.last_camera_position, position)
                print(f"Distance from last position: {distance:.2f}m")
            
            if self.last_camera_rotation_y is not None and current_rotation_y is not None:
                rotation_diff = self._calculate_rotation_difference(self.last_camera_rotation_y, current_rotation_y)
                if rotation_diff is not None:
                    print(f"Rotation difference from last: {rotation_diff:.2f}°")
                else:
                    print(f"Rotation difference from last: Unable to calculate")
            
            print(f"Movement threshold: {self.movement_threshold}m")
            print(f"Rotation threshold: {self.rotation_threshold}°")
            print(f"Debug mode: {self.debug_info}")
            print(f"========================\n")
            
        except Exception as e:
            print(f"Error printing camera status: {e}")
    
    def update_settings(self, **kwargs):
        """更新设置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"Updated {key}: {value}")
        
        # 如果正在运行，重启以应用新设置
        if self.is_running:
            self.stop()
            self.start()
    
    def get_status(self):
        """获取当前状态"""
        return {
            "is_running": self.is_running,
            "enabled": self.enabled,
            "auto_mode": self.auto_mode,
            "timer_interval": self.timer_interval,
            "movement_threshold": self.movement_threshold,
            "rotation_threshold": self.rotation_threshold,
            "distance_threshold": self.distance_threshold,
            "last_camera_position": self.last_camera_position,
            "last_camera_rotation_y": self.last_camera_rotation_y,
            "last_optimization_time": self.last_optimization_time,
            "max_blocks": self.max_blocks, # 新增max_blocks状态
            "use_ray_distance": self.use_ray_distance, # 新增use_ray_distance状态
            "use_cache": self.use_cache # 新增use_cache状态
        }

# 全局优化器实例
_optimizer_instance = None

def create_auto_optimizer(camera_path="/World/Camera",
                         distance_threshold=50.0,
                         fix_coordinate_system=True,
                         debug_info=False,
                         auto_mode="movement",
                         timer_interval=5.0,
                         movement_threshold=1.0,
                         rotation_threshold=45.0,
                         detection_interval=2.0,
                         enabled=True,
                         max_blocks=2, # 新增max_blocks参数
                         use_ray_distance=True, # 是否使用射线距离检测
                         use_cache=True): # 是否使用全局缓存
    """
    创建并返回自动相机优化器实例
    
    Args:
        camera_path: 相机prim路径
        distance_threshold: 距离阈值（米）
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
        auto_mode: 自动模式 ("movement" 或 "timer")
        timer_interval: 定时器间隔（秒）
        movement_threshold: 相机移动检测阈值（米）
        rotation_threshold: y轴旋转角度检测阈值（度）
        enabled: 是否启用自动优化
        max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
    
    Returns:
        AutoCameraOptimizer实例
    """
    global _optimizer_instance
    
    # 如果已有实例，先停止
    if _optimizer_instance:
        _optimizer_instance.stop()
    
    # 创建新实例
    _optimizer_instance = AutoCameraOptimizer(
        camera_path=camera_path,
        distance_threshold=distance_threshold,
        fix_coordinate_system=fix_coordinate_system,
        debug_info=debug_info,
        auto_mode=auto_mode,
        timer_interval=timer_interval,
        movement_threshold=movement_threshold,
        rotation_threshold=rotation_threshold,
        detection_interval=detection_interval,
        enabled=enabled,
        max_blocks=max_blocks, # 传递max_blocks参数
        use_ray_distance=use_ray_distance, # 传递use_ray_distance参数
        use_cache=use_cache # 传递use_cache参数
    )
    
    return _optimizer_instance

def start_auto_optimization(camera_path="/World/Camera",
                          distance_threshold=50.0,
                          fix_coordinate_system=True,
                          debug_info=False,
                          auto_mode="movement",
                          timer_interval=5.0,
                          movement_threshold=1.0,
                          rotation_threshold=45.0,
                          detection_interval=2.0,
                          max_blocks=2, # 新增max_blocks参数
                          use_ray_distance=True, # 是否使用射线距离检测
                          use_cache=True): # 是否使用全局缓存
    """
    启动自动相机优化
    
    Args:
        camera_path: 相机prim路径
        distance_threshold: 距离阈值（米）
        fix_coordinate_system: 是否应用坐标系修正
        debug_info: 是否输出调试信息
        auto_mode: 自动模式 ("movement" 或 "timer")
        timer_interval: 定时器间隔（秒）
        movement_threshold: 相机移动检测阈值（米）
        rotation_threshold: y轴旋转角度检测阈值（度）
        max_blocks: 最大同时显示的block数量（默认2个）
        use_ray_distance: 是否使用射线距离检测（默认True）
        use_cache: 是否使用全局缓存（默认True）
    
    Returns:
        AutoCameraOptimizer实例
    """
    optimizer = create_auto_optimizer(
        camera_path=camera_path,
        distance_threshold=distance_threshold,
        fix_coordinate_system=fix_coordinate_system,
        debug_info=debug_info,
        auto_mode=auto_mode,
        timer_interval=timer_interval,
        movement_threshold=movement_threshold,
        rotation_threshold=rotation_threshold,
        detection_interval=detection_interval,
        enabled=True,
        max_blocks=max_blocks, # 传递max_blocks参数
        use_ray_distance=use_ray_distance, # 传递use_ray_distance参数
        use_cache=use_cache # 传递use_cache参数
    )
    
    optimizer.start()
    return optimizer

def stop_auto_optimization():
    """停止自动相机优化"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.stop()
        return True
    return False

def manual_optimize():
    """手动触发优化"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.manual_optimize()
        return True
    return False

def get_optimizer_status():
    """获取优化器状态"""
    global _optimizer_instance
    if _optimizer_instance:
        return _optimizer_instance.get_status()
    return None

def print_camera_status():
    """打印当前相机状态信息"""
    global _optimizer_instance
    if _optimizer_instance:
        _optimizer_instance.print_camera_status()
    else:
        print("No optimizer instance found")

# Standalone模式配置和运行
class StandaloneConfig:
    """Standalone模式的配置类"""
    def __init__(self):
        # USD文件路径配置
        self.usd_file_path = "E:/wanleqi/FlorenzVillage/data/18-concatenated-1.usd"  # 需要用户设置
        
        # 相机配置
        self.camera_path = "/World/Camera"
        
        # 优化器配置
        self.distance_threshold = 50.0
        self.fix_coordinate_system = True
        self.debug_info = False
        self.auto_mode = "movement"  # "movement" 或 "timer"
        self.timer_interval = 5.0
        self.movement_threshold = 1.0
        self.rotation_threshold = 45.0
        self.detection_interval = 0.5  # 检测间隔（秒）
        self.max_blocks = 2
        self.use_ray_distance = True
        self.use_cache = True
        
        # 运行时控制
        self.auto_start_runtime = True  # 是否自动开启运行时
        
        # 相机移动模拟配置
        self.camera_start_position = Gf.Vec3f(0, 0, 10)  # 相机起始位置
        self.camera_target_position = Gf.Vec3f(20, 0, 10)  # 相机目标位置
        self.camera_movement_duration = 30.0  # 移动持续时间（秒）
        self.camera_movement_loop = True  # 是否循环移动

def load_usd_stage(usd_file_path):
    """加载USD文件到stage"""
    try:
        print(f"Loading USD file: {usd_file_path}")
        
        # 检查文件是否存在
        import os
        if not os.path.exists(usd_file_path):
            print(f"ERROR: USD file not found: {usd_file_path}")
            return False
        
        # 使用与 nurec_demo_standalone.py 相同的方法打开stage
        print(f"Opening stage: '{usd_file_path}'")
        omni.usd.get_context().open_stage(usd_file_path)
        stage = omni.usd.get_context().get_stage()
        
        if not stage:
            print(f"ERROR: Failed to open USD file: {usd_file_path}")
            return False
        
        print(f"Successfully loaded USD file: {usd_file_path}")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to load USD file: {e}")
        return False

def start_runtime():
    """启动运行时（播放模式）"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.play()
        print("Runtime started (Play mode)")
        return True
    except Exception as e:
        print(f"ERROR: Failed to start runtime: {e}")
        return False

def stop_runtime():
    """停止运行时"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.stop()
        print("Runtime stopped")
        return True
    except Exception as e:
        print(f"ERROR: Failed to stop runtime: {e}")
        return False

def wait_for_render_initialization(max_wait_time=10.0):
    """等待渲染系统初始化完成"""
    print("Waiting for render system initialization...")
    
    import time
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        # 更新 Isaac Sim 以推进渲染初始化
        simulation_app.update()
        
        # 检查是否有可用的 stage
        stage = omni.usd.get_context().get_stage()
        if stage:
            # 检查是否有 nurec prim
            nurec_prims = []
            for prim in stage.Traverse():
                attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
                attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
                
                if not attr_min or not attr_max:
                    continue
                nurec_prims.append(prim)
            
            if nurec_prims:
                print(f"Found {len(nurec_prims)} NurecPrim(s), render system ready")
                return True
            else:
                print("Stage loaded but no NurecPrim found, continuing to wait...")
        
        time.sleep(0.1)  # 短暂等待
    
    print(f"Warning: Render initialization timeout after {max_wait_time}s")
    return False

def run_standalone_mode(config):
    """运行standalone模式"""
    print("=== Auto Camera Optimizer Standalone Mode ===")
    
    # 1. 检查USD文件路径
    if not config.usd_file_path:
        print("ERROR: Please set USD file path in config.usd_file_path")
        return
    
    # 2. 加载USD文件
    if not load_usd_stage(config.usd_file_path):
        return
    
    # 3. 等待渲染系统初始化
    if not wait_for_render_initialization():
        print("WARNING: Render initialization may not be complete")
    
    # 4. 自动启动运行时（如果配置启用）
    if config.auto_start_runtime:
        if not start_runtime():
            print("WARNING: Failed to start runtime, continuing...")
    
    # 5. 启动自动相机优化器
    print(f"\nStarting Auto Camera Optimizer...")
    optimizer = start_auto_optimization(
        camera_path=config.camera_path,
        distance_threshold=config.distance_threshold,
        fix_coordinate_system=config.fix_coordinate_system,
        debug_info=config.debug_info,
        auto_mode=config.auto_mode,
        timer_interval=config.timer_interval,
        movement_threshold=config.movement_threshold,
        rotation_threshold=config.rotation_threshold,
        detection_interval=config.detection_interval,
        max_blocks=config.max_blocks,
        use_ray_distance=config.use_ray_distance,
        use_cache=config.use_cache
    )
    
    print(f"\n=== Standalone mode setup completed ===")
    print(f"USD file: {config.usd_file_path}")
    print(f"Camera path: {config.camera_path}")
    print(f"Auto mode: {config.auto_mode}")
    print(f"Distance threshold: {config.distance_threshold}m")
    print(f"Movement threshold: {config.movement_threshold}m")
    print(f"Rotation threshold: {config.rotation_threshold}°")
    print(f"Detection interval: {config.detection_interval}s")
    print(f"Max blocks: {config.max_blocks}")
    
    return optimizer

# 示例使用
if __name__ == "__main__":
    print("=== Auto Camera Optimizer Demo ===")

    # 导入必要的模块
    import omni.kit.commands
    import omni.kit.app
    import omni.usd
    import omni.timeline
    from pxr import Usd, UsdGeom, Gf
    
    try:
        # 创建standalone配置
        config = StandaloneConfig()
        
        # ===== 用户配置区域 =====
        # 请修改以下配置项：
        
        # 1. 设置USD文件路径（必需）
        config.usd_file_path = "E:/wanleqi/YangLaoyuan/20-concatenated-1.usd"
        
        # 2. 相机配置（可选）
        config.camera_path = "/World/Camera"  # 相机prim路径
        
        # 3. 优化器配置（可选）
        config.distance_threshold = 10.0  # 距离阈值（米）
        config.auto_mode = "movement"  # "movement" 或 "timer"
        config.movement_threshold = 2.0  # 相机移动检测阈值（米）
        config.rotation_threshold = 45.0  # y轴旋转角度检测阈值（度）
        config.max_blocks = 3 # 最大同时显示的block数量
        config.use_ray_distance = True # 是否使用射线距离检测
        config.debug_info = True  # 是否输出调试信息
        
        # 4. 运行时控制（可选）
        config.auto_start_runtime = True  # 是否自动开启运行时
        
        # 5. 相机移动模拟配置（可选）
        config.camera_start_position = Gf.Vec3f(0, 0, 10)  # 相机起始位置
        config.camera_target_position = Gf.Vec3f(20, 0, 10)  # 相机目标位置
        config.camera_movement_duration = 30.0  # 移动持续时间（秒）
        config.camera_movement_loop = True  # 是否循环移动
        
        # ===== 配置结束 =====
        
        # 运行standalone模式
        if config.usd_file_path != "C:/test-usd-path":
            optimizer = run_standalone_mode(config)
            
            # 保持脚本运行（可选）
            print("\\nPress Ctrl+C to stop...")
            try:
                # 确保渲染系统完全初始化后再开始主循环
                # print("Ensuring render system is fully initialized...")
                # for i in range(50):  # 额外等待5秒确保渲染系统完全就绪
                #     simulation_app.update()
                #     time.sleep(0.1)
                
                print("Starting main loop...")
                while True:
                    simulation_app.update()  # 使用 simulation_app.update() 而不是 time.sleep()
                    time.sleep(0.01)  # 短暂延迟以避免过度占用CPU
            except KeyboardInterrupt:
                print("\\nStopping...")
                if optimizer:
                    optimizer.stop()
        else:
            print("\\nERROR: Please set config.usd_file_path to your actual USD file path")
            print("Example:")
            print("config.usd_file_path = 'C:/Users/<USER>/Documents/scene.usd'")
            print("or")
            print("config.usd_file_path = '/home/<USER>/scene.usd'")
    
    except Exception as e:
        print(f"\\nERROR: An error occurred: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理 Isaac Sim SimulationApp
        if simulation_app is not None:
            print("\\nCleaning up Isaac Sim SimulationApp...")
            simulation_app.close()
            print("✓ Isaac Sim SimulationApp closed")
        
        print("\\n=== Script execution completed ===")