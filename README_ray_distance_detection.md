# 基于相机Forward方向的射线距离检测

## 功能概述

本功能实现了基于相机forward方向的射线距离检测机制，用于优化nurec prim的显示/隐藏。相比传统的欧几里得距离检测，射线距离检测更符合相机的视角特性，能够更准确地判断哪些nurec prim应该被显示。

## 主要特性

1. **射线距离检测**: 从相机位置沿forward方向发射射线，计算与nurec prim包围盒的交点距离
2. **包围盒内优先显示**: 确保相机当前所在的包围盒内的nurec prim肯定显示
3. **maxBlocks限制**: 遵循最大显示块数限制，优先显示距离最近的块
4. **向后兼容**: 保留传统距离检测机制，可通过参数切换
5. **实时检测**: 支持相机移动检测和定时触发两种模式

## 核心算法

### 射线-包围盒相交检测

使用经典的射线-包围盒相交算法（Ray-Box Intersection）：

1. 从相机位置发射沿forward方向的射线
2. 计算射线与包围盒各面的交点参数
3. 确定最近的交点距离
4. 如果射线不命中包围盒，返回None

### 显示判定逻辑

```python
# 判断是否应该显示：
# 1. 如果相机在包围盒内，肯定显示
# 2. 如果射线命中包围盒且距离在阈值内，且是最近的max_blocks个之一，则显示
# 3. 否则隐藏
```

## 使用方法

### 1. 基本使用

```python
from optimizers.auto_camera_optimizer import optimize_by_camera_distance

# 使用射线距离检测
optimize_by_camera_distance(
    camera_path="/World/Camera",
    distance_threshold=50.0,
    max_blocks=2,
    use_ray_distance=True  # 启用射线距离检测
)

# 使用传统距离检测（对比）
optimize_by_camera_distance(
    camera_path="/World/Camera",
    distance_threshold=50.0,
    max_blocks=2,
    use_ray_distance=False  # 使用传统距离检测
)
```

### 2. 自动优化器

```python
from optimizers.auto_camera_optimizer import start_auto_optimization

# 启动自动优化器（射线距离检测模式）
optimizer = start_auto_optimization(
    camera_path="/World/Camera",
    distance_threshold=50.0,
    auto_mode="movement",  # 相机移动检测模式
    movement_threshold=2.0,  # 相机移动2米以上时触发
    max_blocks=2,
    use_ray_distance=True  # 启用射线距离检测
)

# 手动触发优化
from optimizers.auto_camera_optimizer import manual_optimize
manual_optimize()

# 停止优化器
from optimizers.auto_camera_optimizer import stop_auto_optimization
stop_auto_optimization()
```

### 3. 高级配置

```python
from optimizers.auto_camera_optimizer import create_auto_optimizer

# 创建自定义优化器
optimizer = create_auto_optimizer(
    camera_path="/World/Camera",
    distance_threshold=50.0,
    fix_coordinate_system=True,  # 应用坐标系修正
    debug_info=True,  # 输出调试信息
    auto_mode="timer",  # 定时器模式
    timer_interval=5.0,  # 每5秒触发一次
    max_blocks=3,  # 最多显示3个块
    use_ray_distance=True  # 启用射线距离检测
)

# 启动优化器
optimizer.start()

# 更新设置
optimizer.update_settings(
    distance_threshold=30.0,
    max_blocks=4
)

# 获取状态
status = optimizer.get_status()
print(f"优化器状态: {status}")
```

## 参数说明

### optimize_by_camera_distance 参数

- `stage`: USD stage对象
- `camera_path`: 相机prim路径（默认"/World/Camera"）
- `distance_threshold`: 距离阈值，单位米（默认50.0）
- `fix_coordinate_system`: 是否应用坐标系修正（默认True）
- `debug_info`: 是否输出调试信息（默认False）
- `max_blocks`: 最大同时显示的block数量（默认2）
- `use_ray_distance`: 是否使用射线距离检测（默认True）

### AutoCameraOptimizer 参数

- `camera_path`: 相机prim路径
- `distance_threshold`: 距离阈值（米）
- `fix_coordinate_system`: 是否应用坐标系修正
- `debug_info`: 是否输出调试信息
- `auto_mode`: 自动模式（"movement" 或 "timer"）
- `timer_interval`: 定时器间隔（秒）
- `movement_threshold`: 相机移动检测阈值（米）
- `enabled`: 是否启用自动优化
- `max_blocks`: 最大同时显示的block数量
- `use_ray_distance`: 是否使用射线距离检测

## 算法优势

### 相比传统距离检测的优势

1. **视角相关性**: 只考虑相机forward方向上的距离，更符合视觉感知
2. **遮挡感知**: 能够感知到被其他物体遮挡的nurec prim
3. **性能优化**: 减少不必要的渲染，提高性能
4. **用户体验**: 更自然的显示/隐藏效果

### 适用场景

- 大型场景的LOD（细节层次）管理
- 实时渲染性能优化
- 基于视角的内容流式加载
- 虚拟现实/增强现实应用

## 测试

运行测试脚本验证功能：

```bash
python test_ray_distance_detection.py
```

测试内容包括：
1. 射线距离检测功能测试
2. 自动优化器与射线距离检测集成测试
3. 传统距离检测对比测试

## 注意事项

1. **坐标系修正**: 默认启用坐标系修正，适用于NUREC坐标系到USD坐标系的转换
2. **性能考虑**: 射线检测相比传统距离检测有轻微的性能开销，但通常可以忽略
3. **调试模式**: 启用debug_info可以查看详细的检测过程和结果
4. **错误处理**: 如果无法获取相机forward方向，会自动回退到传统距离检测

## 技术实现

### 核心函数

- `get_camera_forward_direction()`: 获取相机forward方向向量
- `ray_bbox_intersection()`: 射线与包围盒相交检测
- `calculate_ray_distance_to_bbox()`: 计算射线距离到包围盒
- `optimize_by_camera_distance()`: 主优化函数

### 数据结构

```python
block_info = {
    'parent2': parent_prim,
    'distance': ray_distance,
    'minB': bbox_min,
    'maxB': bbox_max,
    'path': prim_path,
    'distance_method': 'ray'  # 或 'traditional'
}
```

## 版本历史

- v1.0: 初始实现，支持射线距离检测
- 保留传统距离检测机制，确保向后兼容
- 支持自动优化器和手动触发两种模式 