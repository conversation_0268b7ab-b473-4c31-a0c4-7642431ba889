import omni.usd

STAGE = omni.usd.get_context().get_stage()
EVENT_STREAM = omni.usd.get_context().get_stage_event_stream()

LOD_PRIMS = ["/World/LOD0", "/World/LOD1", "/World/LOD2"]

def reload_active_lod():
    for path in LOD_PRIMS:
        prim = STAGE.GetPrimAtPath(path)
        if prim and prim.IsActive():
            print(f"🔁 Reloading active prim: {path}")
            STAGE.Unload(path)
            STAGE.Load(path)

def on_stage_event(event):
    if event.type == int(omni.usd.StageEventType.VARIANT_CHANGED):
        print("🎯 Variant change detected!")
        reload_active_lod()

# 注册监听器
EVENT_STREAM.create_subscription_to_pop(on_stage_event, name="LOD_variant_listener")
print("✅ 成功监听 variant 变化并绑定 reload。")
