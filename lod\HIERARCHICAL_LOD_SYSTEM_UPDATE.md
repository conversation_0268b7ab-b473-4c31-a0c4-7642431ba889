# 分层 LOD 系统更新说明

## 概述

已成功将 `simple_tileset_lod_example_standalone.py` 更新为支持新的分层 3D Tiles 结构，替代了原来的平铺 LOD 结构。

## 主要修改

### 1. Stage 结构适配

#### 原来的平铺结构：
```
/World/TilesetRegion
├── LOD_High
├── LOD_Medium
└── LOD_Low
```

#### 新的分层结构：
```
/World/TilesetRegion
└── Tile_0_0 (根容器)
    └── Tile_1_0 (depth=1)
        ├── Content_LOD_15 -> 15/tile_15_16383_16384.usdz
        └── Tile_2_0 (depth=2)
            ├── Content_LOD_16 -> 16/tile_16_32767_32768.usdz
            └── Tile_3_0 (depth=3)
                ├── Content_LOD_17 -> 17/tile_17_65535_65536.usdz
                ├── Tile_4_0 (depth=4)
                │   ├── Content_LOD_18 -> 18/tile_18_131070_131072.usdz
                │   └── Tile_5_0 (depth=5)
                │       ├── Content_LOD_19 -> 19/tile_19_262141_262145.usdz
                │       ├── Tile_6_0 (depth=6)
                │       │   └── Content_LOD_20 -> 20/tile_20_524282_524291.usdz
                │       └── Tile_6_1 (depth=6)
                │           └── Content_LOD_20 -> 20/tile_20_524283_524291.usdz
                └── ... (更多分支)
```

### 2. 关键函数更新

#### A. `get_tileset_region_bounds_from_stage()`
- **修改前**: 查找 `LOD_` 开头的 prims 和 `omni:nurec:crop:*` 属性
- **修改后**: 递归查找 `tileset:minBounds` 和 `tileset:maxBounds` 属性

#### B. `collect_all_tiles_from_stage()` (新增)
- 递归收集所有 `Tile_` 开头的节点
- 提取 tile 属性：`tileset:depth`, `tileset:geometricError`, `tileset:hasContent`
- 收集内容节点：`Content_LOD_` 开头的子节点
- 提取内容属性：`tileset:lodLevel`, `tileset:uri`

#### C. `update_tileset_lod_visibility_hierarchical()` (新增)
- 替代原来的 `update_tileset_lod_visibility()`
- 基于 LOD 级别进行智能可见性控制
- 支持容差机制（±2级别）
- 提供详细的调试信息

#### D. `check_current_tileset_lod_status()` (更新)
- 显示完整的 tile 层次结构状态
- 区分可见和隐藏的内容节点
- 提供深度和 LOD 级别信息

### 3. LOD 选择策略

#### 新的分层 LOD 选择逻辑：
```python
# 将 LOD 名称转换为数值级别
lod_name_to_level = {"High": 20, "Medium": 18, "Low": 15}
target_lod_level = lod_name_to_level.get(selected_lod_name, 15)

# 为每个内容节点决定可见性
for content in tile['content_nodes']:
    content_lod_level = content.get('lod_level', 15)
    level_diff = abs(content_lod_level - target_lod_level)
    visible = level_diff <= 2  # 允许±2级别的容差
```

#### 优势：
- **渐进式切换**: 支持多个 LOD 级别同时可见
- **平滑过渡**: 避免突然的 LOD 跳跃
- **性能优化**: 只显示相关的 LOD 级别

### 4. 属性映射

#### 新的 Tile 属性：
- `tileset:depth` - tile 在层次结构中的深度
- `tileset:geometricError` - 几何误差值
- `tileset:hasContent` - 是否包含实际内容
- `tileset:minBounds` / `tileset:maxBounds` - 边界框

#### 新的 Content 属性：
- `tileset:lodLevel` - LOD 级别（数值）
- `tileset:uri` - 原始 USDZ 文件路径

### 5. 向后兼容性

保持了向后兼容性：
```python
# 保持向后兼容性的别名
def update_tileset_lod_visibility(stage, region_bounds, scheduler, verbose=True):
    """向后兼容的LOD更新函数"""
    return update_tileset_lod_visibility_hierarchical(stage, region_bounds, scheduler, verbose)
```

## 测试验证

### 测试结果：
- ✅ 成功收集 27 个 tiles（1个根容器 + 26个内容 tiles）
- ✅ 正确识别 6 个 LOD 级别（15-20）
- ✅ 验证了 7 个深度层级（0-6）
- ✅ LOD 选择逻辑工作正常
- ✅ 可见性控制准确

### LOD 分布：
- **LOD 20**: 13 个内容节点（最高质量）
- **LOD 19**: 6 个内容节点
- **LOD 18**: 4 个内容节点
- **LOD 17**: 1 个内容节点
- **LOD 16**: 1 个内容节点
- **LOD 15**: 1 个内容节点（最低质量）

## 使用方法

### 1. 创建分层 USD 场景
```python
# 在 Isaac Sim Script Editor 中运行
from tileset_usd_creator import create_tileset_scene
result = create_tileset_scene()
```

### 2. 启动分层 LOD 系统
```python
# 运行更新后的 standalone 脚本
python simple_tileset_lod_example_standalone.py
```

### 3. 验证系统状态
系统会自动：
- 收集所有 tile 节点
- 基于相机距离选择合适的 LOD
- 更新内容节点的可见性
- 提供实时调试信息

## 优势总结

### 1. 真正的 3D Tiles 支持
- ✅ 保持空间层次关系
- ✅ 支持复杂的 tile 分割
- ✅ 符合 3D Tiles 规范

### 2. 智能 LOD 管理
- ✅ 基于几何误差的精确选择
- ✅ 渐进式 LOD 切换
- ✅ 容差机制避免闪烁

### 3. 性能优化
- ✅ 只加载必要的内容
- ✅ 支持空间裁剪
- ✅ 高效的可见性控制

### 4. 调试友好
- ✅ 详细的状态信息
- ✅ 清晰的层次结构显示
- ✅ 实时性能指标

现在的系统完全支持新的分层 3D Tiles 结构，提供了更好的性能、更准确的 LOD 控制和更符合标准的实现！
