"""
Tileset工具类
包含视锥体检测、批量显示等工具方法
"""

import math
import time
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
from pxr import UsdGeom, Gf


class FrustumCuller:
    """视锥体剔除工具类"""
    
    @staticmethod
    def extract_camera_frustum_planes(camera_prim) -> Tuple[Optional[List], Optional[Gf.Vec3f], Optional[Gf.Vec3f]]:
        """从相机 prim 中提取视锥体平面"""
        try:
            # 获取相机的变换矩阵
            xformable = UsdGeom.Xformable(camera_prim)
            transform_matrix = xformable.GetLocalTransformation()

            # 获取相机属性
            camera = UsdGeom.Camera(camera_prim)

            # 获取视野角度（水平FOV）
            horizontal_aperture = camera.GetHorizontalApertureAttr().Get() or 20.955  # 默认值
            vertical_aperture = camera.GetVerticalApertureAttr().Get() or 15.2908    # 默认值
            focal_length = camera.GetFocalLengthAttr().Get() or 50.0                 # 默认值

            # 计算视野角度（弧度）
            h_fov = 2.0 * math.atan(horizontal_aperture / (2.0 * focal_length))
            v_fov = 2.0 * math.atan(vertical_aperture / (2.0 * focal_length))

            # 获取近远平面距离
            near_distance = camera.GetClippingRangeAttr().Get()[0] if camera.GetClippingRangeAttr().Get() else 0.1
            far_distance = camera.GetClippingRangeAttr().Get()[1] if camera.GetClippingRangeAttr().Get() else 1000.0

            # 提取相机位置和方向
            camera_pos = Gf.Vec3f(transform_matrix.ExtractTranslation())

            # 提取旋转矩阵并计算方向向量
            rotation_matrix = transform_matrix.ExtractRotationMatrix()

            # USD 相机默认朝向 -Z 方向
            forward = -Gf.Vec3f(rotation_matrix[2][0], rotation_matrix[2][1], rotation_matrix[2][2])
            up = Gf.Vec3f(rotation_matrix[1][0], rotation_matrix[1][1], rotation_matrix[1][2])
            right = Gf.Vec3f(rotation_matrix[0][0], rotation_matrix[0][1], rotation_matrix[0][2])

            # 计算视锥体的6个平面（法向量指向内部）
            planes = []

            # 近平面
            near_center = camera_pos + forward * near_distance
            planes.append((forward, near_center))

            # 远平面
            far_center = camera_pos + forward * far_distance
            planes.append((-forward, far_center))

            # 左平面
            half_h_fov = h_fov * 0.5
            left_normal = math.cos(half_h_fov) * right + math.sin(half_h_fov) * forward
            left_normal = left_normal.GetNormalized()
            planes.append((left_normal, camera_pos))

            # 右平面
            right_normal = -math.cos(half_h_fov) * right + math.sin(half_h_fov) * forward
            right_normal = right_normal.GetNormalized()
            planes.append((right_normal, camera_pos))

            # 上平面
            half_v_fov = v_fov * 0.5
            top_normal = -math.cos(half_v_fov) * up + math.sin(half_v_fov) * forward
            top_normal = top_normal.GetNormalized()
            planes.append((top_normal, camera_pos))

            # 下平面
            bottom_normal = math.cos(half_v_fov) * up + math.sin(half_v_fov) * forward
            bottom_normal = bottom_normal.GetNormalized()
            planes.append((bottom_normal, camera_pos))

            return planes, camera_pos, forward

        except Exception as e:
            print(f"Error extracting camera frustum: {e}")
            return None, None, None

    @staticmethod
    def is_bounding_box_in_frustum(bbox, frustum_planes) -> bool:
        """检查边界框是否与视锥体相交"""
        if not bbox or not frustum_planes:
            return True  # 如果无法检测，默认可见

        try:
            # 获取边界框的8个顶点
            min_pt = bbox.min_point
            max_pt = bbox.max_point

            vertices = [
                Gf.Vec3f(min_pt[0], min_pt[1], min_pt[2]),
                Gf.Vec3f(max_pt[0], min_pt[1], min_pt[2]),
                Gf.Vec3f(min_pt[0], max_pt[1], min_pt[2]),
                Gf.Vec3f(max_pt[0], max_pt[1], min_pt[2]),
                Gf.Vec3f(min_pt[0], min_pt[1], max_pt[2]),
                Gf.Vec3f(max_pt[0], min_pt[1], max_pt[2]),
                Gf.Vec3f(min_pt[0], max_pt[1], max_pt[2]),
                Gf.Vec3f(max_pt[0], max_pt[1], max_pt[2])
            ]

            # 对每个平面进行测试
            for plane_normal, plane_point in frustum_planes:
                # 检查所有顶点是否都在平面的外侧
                all_outside = True
                for vertex in vertices:
                    # 计算点到平面的距离
                    to_vertex = vertex - plane_point
                    distance = Gf.Dot(to_vertex, plane_normal)

                    if distance >= 0:  # 顶点在平面内侧或平面上
                        all_outside = False
                        break

                # 如果所有顶点都在某个平面外侧，则边界框完全在视锥体外
                if all_outside:
                    return False

            # 如果没有被任何平面完全排除，则认为相交
            return True

        except Exception as e:
            print(f"Error checking bounding box in frustum: {e}")
            return True  # 出错时默认可见


class BatchDisplayManager:
    """批量显示管理器"""
    
    @staticmethod
    def display_tiles_in_batches_by_tile_index(visible_items: List[Dict], batch_interval: float, verbose: bool = True):
        """按tile_index分批显示瓦片，相同tile_index的瓦片作为一个批次"""
        if not visible_items:
            return

        # 按tile_index分组
        tile_index_groups = defaultdict(list)
        for item in visible_items:
            tile_index_groups[item['tile_index']].append(item)

        # 按tile_index排序（从小到大）
        sorted_tile_indices = sorted(tile_index_groups.keys())

        if verbose:
            print(f"📦 Grouped tiles by tile_index:")
            for tile_index in sorted_tile_indices:
                items = tile_index_groups[tile_index]
                print(f"  tile_index {tile_index}: {len(items)} tiles")

        # 逐个tile_index批次显示
        for batch_idx, tile_index in enumerate(sorted_tile_indices):
            batch_items = tile_index_groups[tile_index]

            if verbose:
                print(f"📦 Batch {batch_idx + 1}/{len(sorted_tile_indices)}: Showing all tiles with tile_index={tile_index} ({len(batch_items)} tiles)")

            # 显示当前tile_index的所有瓦片
            for item in batch_items:
                content = item['content']
                content_prim = content['prim']
                imageable = UsdGeom.Imageable(content_prim)
                vis_attr = imageable.GetVisibilityAttr()
                vis_attr.Set(UsdGeom.Tokens.inherited)

                if verbose:
                    print(f"  ✅ Showing tile: {content['name']} (tile_index: {item['tile_index']}) at {item['tile']['name']}")

            # 如果不是最后一批，等待指定间隔
            if batch_idx < len(sorted_tile_indices) - 1:
                if verbose:
                    print(f"⏳ Waiting {batch_interval}s before next tile_index batch...")
                time.sleep(batch_interval)

        if verbose:
            print(f"✅ Batch display completed! All {len(visible_items)} tiles are now visible.")
            print(f"💡 Displayed in tile_index order: {sorted_tile_indices}")
