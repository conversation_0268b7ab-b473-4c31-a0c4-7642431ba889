# 精确 LOD 控制和视锥体裁剪功能

## 概述

已成功实现了两个关键功能：
1. **精确 LOD 控制** - 确保只显示对应层级的 LOD Tile prims
2. **视锥体裁剪** - 只显示相机视野内的 tile prims

## 问题解决

### 1. 原来的 LOD 控制问题

**问题**：
- Medium LOD 会显示所有层级的 LOD Tile prims
- Low LOD 会显示 15、16、17 三个层级的 tile prims
- 使用了 `±2 级别容差`，导致多个 LOD 级别同时可见

**原来的代码**：
```python
# 允许±2级别的容差
level_diff = abs(content_lod_level - target_lod_level)
visible = level_diff <= 2  # 这导致了多个级别可见
```

**修复后的代码**：
```python
# 精确LOD匹配：只显示目标LOD级别的内容
lod_match = (content_lod_level == target_lod_level)
```

### 2. 新增视锥体裁剪功能

**功能**：
- 提取相机的视锥体平面
- 检查 tile 边界框是否与视锥体相交
- 只显示在相机视野内的 tiles

## 核心功能实现

### 1. 视锥体提取函数

```python
def extract_camera_frustum_planes(camera_prim):
    """从相机 prim 中提取视锥体平面"""
    # 获取相机参数：FOV、近远平面、变换矩阵
    # 计算6个视锥体平面：近、远、左、右、上、下
    # 返回平面法向量和参考点
```

**关键特性**：
- 支持 USD 相机的标准参数
- 自动计算水平和垂直视野角
- 生成6个裁剪平面（近、远、左、右、上、下）

### 2. 边界框相交检测

```python
def is_bounding_box_in_frustum(bbox, frustum_planes):
    """检查边界框是否与视锥体相交"""
    # 获取边界框的8个顶点
    # 对每个视锥体平面进行测试
    # 如果所有顶点都在某个平面外侧，则完全在视锥体外
```

**算法**：
- 使用分离轴定理的简化版本
- 检查边界框的8个顶点
- 保守估计：只要不能确定完全在外，就认为相交

### 3. 精确 LOD 可见性控制

```python
def update_tileset_lod_visibility_hierarchical(stage, region_bounds, scheduler, verbose=True):
    # 1. 提取相机视锥体
    frustum_planes, cam_pos, cam_forward = extract_camera_frustum_planes(camera)
    
    # 2. 选择目标 LOD 级别
    target_lod_level = lod_name_to_level.get(selected_lod_name, 15)
    
    # 3. 对每个 tile 进行双重检查
    for tile in all_tiles:
        for content in tile['content_nodes']:
            # 精确LOD匹配
            lod_match = (content_lod_level == target_lod_level)
            
            # 视锥体检测
            frustum_visible = is_bounding_box_in_frustum(tile['bounds'], frustum_planes)
            
            # 最终可见性：LOD匹配 AND 在视锥体内
            visible = lod_match and frustum_visible
```

## 功能对比

### 修复前 vs 修复后

| 功能 | 修复前 | 修复后 |
|------|--------|--------|
| LOD 控制 | ±2级别容差，多个级别可见 | 精确匹配，只显示目标级别 |
| 视锥体裁剪 | 无 | 完整的6平面视锥体检测 |
| 性能 | 渲染不必要的内容 | 只渲染必要的内容 |
| 调试信息 | 基本统计 | 详细的可见/隐藏/裁剪统计 |

### LOD 级别映射

```python
lod_name_to_level = {
    "High": 20,    # 只显示 LOD 20
    "Medium": 18,  # 只显示 LOD 18  
    "Low": 15      # 只显示 LOD 15
}
```

## 测试验证

### 1. 精确 LOD 控制测试

```
目标 LOD: Medium (级别 18)
  ❌ LOD 15: 隐藏 (不匹配)
  ❌ LOD 16: 隐藏 (不匹配)
  ❌ LOD 17: 隐藏 (不匹配)
  ✅ LOD 18: 可见 (精确匹配)  ← 只有这个可见
  ❌ LOD 19: 隐藏 (不匹配)
  ❌ LOD 20: 隐藏 (不匹配)
```

### 2. 视锥体裁剪测试

```
目标 LOD: High (级别 20)
  ✅ Tile_3 (LOD 20, 前方): 可见        ← LOD匹配 + 视野内
  🚫 Tile_6 (LOD 20, 右侧): 视锥体裁剪  ← LOD匹配但视野外
```

## 性能优势

### 1. 减少渲染负载
- **修复前**: Medium LOD 可能显示 LOD 16, 17, 18, 19, 20 (5个级别)
- **修复后**: Medium LOD 只显示 LOD 18 (1个级别)

### 2. 视锥体裁剪收益
- 只渲染相机视野内的 tiles
- 大幅减少不可见内容的处理
- 提高帧率和响应性

### 3. 内存优化
- 减少同时激活的几何体数量
- 降低 GPU 内存使用
- 更好的缓存局部性

## 调试信息增强

### 新的输出格式

```
=== Updating Hierarchical Tileset LOD Visibility with Frustum Culling ===
  ✅ Visible: Content_LOD_18 (LOD 18) at Tile_4_1
  🚫 Frustum culled: Content_LOD_18 (LOD 18) at Tile_4_2

LOD Update Summary:
  Target LOD: Medium (level 18)
  Camera distance: 45.7
  Screen space error: 12.34
  Visible content nodes: 3
  Hidden content nodes: 23
  Frustum culled nodes: 5
  Frustum culling: Enabled
```

## 使用方法

### 1. 自动运行
修改后的系统会自动应用新的精确控制和视锥体裁剪：

```python
# 运行修改后的 standalone 脚本
python simple_tileset_lod_example_standalone.py
```

### 2. 配置选项
可以通过修改代码来调整行为：

```python
# 禁用视锥体裁剪（如果需要）
frustum_visible = True  # 强制设为 True

# 调整 LOD 级别映射
lod_name_to_level = {
    "High": 19,    # 改为显示 LOD 19 而不是 20
    "Medium": 17,  # 改为显示 LOD 17 而不是 18
    "Low": 15      # 保持 LOD 15
}
```

## 总结

### ✅ 已实现的功能

1. **精确 LOD 控制**
   - 只显示目标 LOD 级别的内容
   - 消除了多级别同时可见的问题

2. **视锥体裁剪**
   - 6平面视锥体检测
   - 边界框相交测试
   - 自动裁剪视野外的内容

3. **性能优化**
   - 大幅减少渲染负载
   - 更好的内存使用
   - 提高帧率

4. **调试增强**
   - 详细的可见性统计
   - 裁剪原因说明
   - 实时性能指标

### 🎯 效果

现在的系统能够：
- ✅ Medium LOD 只显示 LOD 18 级别的 tiles
- ✅ Low LOD 只显示 LOD 15 级别的 tiles  
- ✅ 只渲染相机视野内的 tiles
- ✅ 提供详细的调试信息
- ✅ 显著提高渲染性能

你现在可以运行修改后的系统，应该能看到精确的 LOD 控制和视锥体裁剪效果！
